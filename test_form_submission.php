<?php
/**
 * Test Form Submission System
 * Comprehensive test for form data collection from shared pages
 */

require_once 'config/database.php';

echo "<h1>Form Submission System Test</h1>";

try {
    $database = new Database();
    $db = $database->getConnection();
    
    if (!$db) {
        throw new Exception("Database connection failed");
    }
    
    echo "<p>✅ Database connection successful</p>";
    
    // Ensure form_submissions table exists
    echo "<h2>Database Setup</h2>";
    
    $sql = "CREATE TABLE IF NOT EXISTS form_submissions (
        id INT AUTO_INCREMENT PRIMARY KEY,
        page_id INT NOT NULL,
        form_id INT NULL,
        share_id INT NULL,
        form_data JSON NOT NULL,
        ip_address VARCHAR(45),
        user_agent TEXT,
        referrer TEXT,
        submitted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        processed BOOLEAN DEFAULT FALSE,
        notes TEXT,
        INDEX idx_page_id (page_id),
        INDEX idx_form_id (form_id),
        INDEX idx_share_id (share_id),
        INDEX idx_submitted_at (submitted_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $db->exec($sql);
    echo "<p>✅ form_submissions table created/verified</p>";
    
    // Check if share_access_log table exists
    $sql = "CREATE TABLE IF NOT EXISTS share_access_log (
        id INT AUTO_INCREMENT PRIMARY KEY,
        share_id INT NOT NULL,
        ip_address VARCHAR(45),
        user_agent TEXT,
        referrer TEXT,
        access_type ENUM('view', 'form_submission', 'download') DEFAULT 'view',
        accessed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_share_id (share_id),
        INDEX idx_accessed_at (accessed_at),
        INDEX idx_access_type (access_type)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $db->exec($sql);
    echo "<p>✅ share_access_log table created/verified</p>";
    
    // Test form submission endpoint
    echo "<h2>Testing Form Submission Endpoint</h2>";
    
    // Get a test share
    $sql = "SELECT ps.*, p.title as page_title 
            FROM page_shares ps 
            JOIN pages p ON ps.page_id = p.id 
            WHERE ps.is_active = 1 AND ps.show_forms = 1 
            LIMIT 1";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $testShare = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($testShare) {
        echo "<p>✅ Found test share: {$testShare['page_title']}</p>";
        echo "<p><strong>Share Token:</strong> {$testShare['share_token']}</p>";
        
        // Simulate a form submission
        $testData = [
            '_share_token' => $testShare['share_token'],
            '_page_id' => $testShare['page_id'],
            '_form_name' => 'test_form',
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'message' => 'This is a test form submission'
        ];
        
        // Test the submission handler directly
        echo "<h3>Simulating Form Submission</h3>";
        
        // Prepare form data (exclude system fields)
        $formData = $testData;
        $systemFields = ['_share_token', '_form_id', '_page_id', '_form_name'];
        foreach ($systemFields as $field) {
            unset($formData[$field]);
        }
        
        // Store form submission
        $sql = "INSERT INTO form_submissions (
                    page_id, form_id, share_id, form_data, ip_address, 
                    user_agent, referrer, submitted_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, NOW())";
        
        $stmt = $db->prepare($sql);
        $stmt->execute([
            $testShare['page_id'],
            null, // form_id
            $testShare['id'],
            json_encode($formData),
            '127.0.0.1', // test IP
            'Test User Agent',
            'http://test.com'
        ]);
        
        $submissionId = $db->lastInsertId();
        echo "<p>✅ Test submission created with ID: $submissionId</p>";
        
        // Verify the submission was stored correctly
        $sql = "SELECT * FROM form_submissions WHERE id = ?";
        $stmt = $db->prepare($sql);
        $stmt->execute([$submissionId]);
        $submission = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($submission) {
            echo "<p>✅ Submission verified in database</p>";
            echo "<div style='background: #f5f5f5; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
            echo "<strong>Stored Data:</strong><br>";
            echo "<pre>" . json_encode(json_decode($submission['form_data']), JSON_PRETTY_PRINT) . "</pre>";
            echo "</div>";
        }
        
    } else {
        echo "<p>⚠️ No test share found. Creating one...</p>";
        
        // Get a page to share
        $sql = "SELECT * FROM pages LIMIT 1";
        $stmt = $db->prepare($sql);
        $stmt->execute();
        $page = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($page) {
            require_once 'includes/sharing_manager.php';
            
            $manager = new SharingManager();
            $result = $manager->createShare($page['id'], [
                'title' => 'Test Form Submission Share',
                'description' => 'Created for testing form submissions',
                'show_forms' => true
            ]);
            
            if ($result['success']) {
                echo "<p>✅ Test share created: <a href='{$result['share']['share_url']}' target='_blank'>{$result['share']['share_url']}</a></p>";
            }
        }
    }
    
    // Show existing submissions
    echo "<h2>Existing Form Submissions</h2>";
    
    $sql = "SELECT 
                fs.*,
                p.title as page_title,
                p.original_filename,
                ps.title as share_title
            FROM form_submissions fs
            LEFT JOIN pages p ON fs.page_id = p.id
            LEFT JOIN page_shares ps ON fs.share_id = ps.id
            ORDER BY fs.submitted_at DESC
            LIMIT 10";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $submissions = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($submissions) > 0) {
        echo "<table border='1' cellpadding='5' style='width: 100%; border-collapse: collapse;'>";
        echo "<tr style='background: #f2f2f2;'>";
        echo "<th>ID</th><th>Page</th><th>Share</th><th>Data</th><th>Submitted</th>";
        echo "</tr>";
        
        foreach ($submissions as $sub) {
            $formData = json_decode($sub['form_data'], true);
            $dataPreview = '';
            if ($formData) {
                $preview = [];
                foreach (array_slice($formData, 0, 3) as $key => $value) {
                    $preview[] = "$key: " . substr($value, 0, 30);
                }
                $dataPreview = implode(', ', $preview);
                if (count($formData) > 3) $dataPreview .= '...';
            }
            
            echo "<tr>";
            echo "<td>{$sub['id']}</td>";
            echo "<td>{$sub['page_title']}<br><small>{$sub['original_filename']}</small></td>";
            echo "<td>" . ($sub['share_title'] ?: 'Direct') . "</td>";
            echo "<td><small>$dataPreview</small></td>";
            echo "<td>{$sub['submitted_at']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>No form submissions found yet.</p>";
    }
    
    // Test the API endpoints
    echo "<h2>Testing API Endpoints</h2>";
    
    $baseUrl = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http') . 
              '://' . $_SERVER['HTTP_HOST'] . 
              rtrim(dirname($_SERVER['SCRIPT_NAME']), '/');
    
    echo "<p><strong>Form Submission Handler:</strong> <a href='{$baseUrl}/submit_form.php' target='_blank'>submit_form.php</a></p>";
    echo "<p><strong>Get Submissions API:</strong> <a href='{$baseUrl}/includes/get_form_submissions.php' target='_blank'>get_form_submissions.php</a></p>";
    echo "<p><strong>Analytics API:</strong> <a href='{$baseUrl}/includes/get_analytics.php' target='_blank'>get_analytics.php</a></p>";
    
} catch (Exception $e) {
    echo "<p>❌ Error: " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "<h2>How to Test Form Submissions</h2>";
echo "<ol>";
echo "<li>Create a share using the main application</li>";
echo "<li>Visit the shared page URL</li>";
echo "<li>Fill out any forms on the page</li>";
echo "<li>Submit the form</li>";
echo "<li>Check this page or the database tab for submissions</li>";
echo "</ol>";

echo "<h2>Quick Links</h2>";
echo "<p><a href='index.html'>Main Application</a></p>";
echo "<p><a href='test_view_fix.php'>Test Sharing System</a></p>";
echo "<p><a href='includes/get_form_submissions.php' target='_blank'>View Submissions API</a></p>";

?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
table { border-collapse: collapse; width: 100%; margin: 10px 0; }
th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
th { background-color: #f2f2f2; }
pre { background: #f5f5f5; padding: 10px; border-radius: 5px; overflow-x: auto; }
</style>
