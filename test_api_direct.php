<?php
/**
 * Direct API Test for Form Submissions
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Direct API Test</h1>";

// Test the sharing manager API directly
require_once 'includes/sharing_manager.php';

try {
    $manager = new SharingManager();
    
    echo "<h2>1. Test get_submissions</h2>";
    $result = $manager->getFormSubmissions();
    
    echo "<h3>API Response:</h3>";
    echo "<pre>";
    print_r($result);
    echo "</pre>";
    
    if ($result['success']) {
        echo "<h3>✅ API Working</h3>";
        echo "<p>Total submissions: " . $result['total'] . "</p>";
        echo "<p>Submissions returned: " . count($result['submissions']) . "</p>";
        echo "<p>Pages available: " . count($result['pages']) . "</p>";
        echo "<p>Forms available: " . count($result['forms']) . "</p>";
        
        if (!empty($result['submissions'])) {
            echo "<h3>Sample Submission Data:</h3>";
            $sample = $result['submissions'][0];
            echo "<pre>";
            print_r($sample);
            echo "</pre>";
            
            // Test the form_data parsing
            echo "<h3>Form Data Analysis:</h3>";
            if (isset($sample['form_data'])) {
                echo "<p><strong>Raw form_data:</strong> " . htmlspecialchars($sample['form_data']) . "</p>";
                
                try {
                    $parsed = json_decode($sample['form_data'], true);
                    if (json_last_error() === JSON_ERROR_NONE) {
                        echo "<p><strong>✅ Valid JSON:</strong></p>";
                        echo "<pre>" . htmlspecialchars(json_encode($parsed, JSON_PRETTY_PRINT)) . "</pre>";
                        
                        // Test preview formatting
                        if (!empty($parsed) && is_array($parsed)) {
                            $fields = array_keys($parsed);
                            $preview = array_slice($fields, 0, 3);
                            $previewText = implode(', ', array_map(function($field) use ($parsed) {
                                return "$field: " . $parsed[$field];
                            }, $preview));
                            echo "<p><strong>Preview:</strong> " . htmlspecialchars($previewText) . "</p>";
                        } else {
                            echo "<p><strong>⚠️ Empty or invalid data structure</strong></p>";
                        }
                    } else {
                        echo "<p><strong>❌ Invalid JSON:</strong> " . json_last_error_msg() . "</p>";
                    }
                } catch (Exception $e) {
                    echo "<p><strong>❌ Parse Error:</strong> " . $e->getMessage() . "</p>";
                }
            } else {
                echo "<p><strong>❌ No form_data field found</strong></p>";
            }
        }
    } else {
        echo "<h3>❌ API Error</h3>";
        echo "<p>" . $result['message'] . "</p>";
    }
    
    // Test individual submission if we have one
    if ($result['success'] && !empty($result['submissions'])) {
        $submissionId = $result['submissions'][0]['id'];
        
        echo "<h2>2. Test get_submission (ID: $submissionId)</h2>";
        $singleResult = $manager->getFormSubmission($submissionId);
        
        echo "<h3>Single Submission Response:</h3>";
        echo "<pre>";
        print_r($singleResult);
        echo "</pre>";
    }
    
} catch (Exception $e) {
    echo "<h3>❌ Exception</h3>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "<h2>3. Database Check</h2>";

try {
    require_once 'config/database.php';
    $database = new Database();
    $db = $database->getConnection();
    
    // Check table structure
    $sql = "DESCRIBE form_submissions";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h3>Table Structure:</h3>";
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>{$column['Field']}</td>";
        echo "<td>{$column['Type']}</td>";
        echo "<td>{$column['Null']}</td>";
        echo "<td>{$column['Key']}</td>";
        echo "<td>{$column['Default']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Check data
    $sql = "SELECT COUNT(*) as total FROM form_submissions";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $count = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo "<h3>Data Count:</h3>";
    echo "<p>Total submissions in database: " . $count['total'] . "</p>";
    
    if ($count['total'] > 0) {
        // Get sample data
        $sql = "SELECT * FROM form_submissions ORDER BY submitted_at DESC LIMIT 1";
        $stmt = $db->prepare($sql);
        $stmt->execute();
        $sample = $stmt->fetch(PDO::FETCH_ASSOC);
        
        echo "<h3>Sample Raw Data:</h3>";
        echo "<pre>";
        print_r($sample);
        echo "</pre>";
    }
    
} catch (Exception $e) {
    echo "<h3>❌ Database Error</h3>";
    echo "<p>" . $e->getMessage() . "</p>";
}

?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h1, h2, h3 { color: #333; }
pre { background: #f5f5f5; padding: 10px; border-radius: 5px; overflow-x: auto; }
table { border-collapse: collapse; margin: 10px 0; }
th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
th { background-color: #f2f2f2; }
</style>
