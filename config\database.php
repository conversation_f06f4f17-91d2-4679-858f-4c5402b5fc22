<?php
/**
 * Database Configuration
 */

class Database {
    private $host = 'localhost';
    private $db_name = 'webpage_manager';
    private $username = 'root';
    private $password = ''; // WAMP default - change if you set a password
    private $conn;

    public function getConnection() {
        $this->conn = null;

        try {
            $this->conn = new PDO(
                "mysql:host=" . $this->host . ";dbname=" . $this->db_name,
                $this->username,
                $this->password
            );
            $this->conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        } catch(PDOException $exception) {
            echo "Connection error: " . $exception->getMessage();
        }

        return $this->conn;
    }

    public function createDatabase() {
        try {
            // Connect without database name first
            $conn = new PDO(
                "mysql:host=" . $this->host,
                $this->username,
                $this->password
            );
            $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

            // Create database if it doesn't exist
            $sql = "CREATE DATABASE IF NOT EXISTS " . $this->db_name . " CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci";
            $conn->exec($sql);

            return true;
        } catch(PDOException $exception) {
            echo "Database creation error: " . $exception->getMessage();
            return false;
        }
    }

    public function createTables() {
        $conn = $this->getConnection();

        if (!$conn) {
            return false;
        }

        try {
            // Application settings table
            $sql = "CREATE TABLE IF NOT EXISTS app_settings (
                id INT AUTO_INCREMENT PRIMARY KEY,
                setting_key VARCHAR(100) NOT NULL UNIQUE,
                setting_value TEXT,
                setting_type ENUM('string', 'integer', 'boolean', 'json') DEFAULT 'string',
                description TEXT,
                is_system BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_setting_key (setting_key)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
            $conn->exec($sql);

            // Users table (for future multi-user support)
            $sql = "CREATE TABLE IF NOT EXISTS users (
                id INT AUTO_INCREMENT PRIMARY KEY,
                username VARCHAR(50) NOT NULL UNIQUE,
                email VARCHAR(255) NOT NULL UNIQUE,
                password_hash VARCHAR(255) NOT NULL,
                full_name VARCHAR(255),
                role ENUM('admin', 'editor', 'viewer') DEFAULT 'editor',
                is_active BOOLEAN DEFAULT TRUE,
                last_login TIMESTAMP NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_username (username),
                INDEX idx_email (email),
                INDEX idx_role (role)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
            $conn->exec($sql);

            // Projects table (for organizing pages into projects)
            $sql = "CREATE TABLE IF NOT EXISTS projects (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(255) NOT NULL,
                description TEXT,
                slug VARCHAR(255) NOT NULL UNIQUE,
                color VARCHAR(7) DEFAULT '#667eea',
                is_active BOOLEAN DEFAULT TRUE,
                created_by INT DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_slug (slug),
                INDEX idx_created_by (created_by),
                FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
            $conn->exec($sql);

            // Enhanced Pages table
            $sql = "CREATE TABLE IF NOT EXISTS pages (
                id INT AUTO_INCREMENT PRIMARY KEY,
                project_id INT DEFAULT NULL,
                filename VARCHAR(255) NOT NULL,
                original_filename VARCHAR(255) NOT NULL,
                title VARCHAR(500),
                description TEXT,
                file_path VARCHAR(500) NOT NULL,
                file_size INT NOT NULL,
                file_hash VARCHAR(64) NOT NULL,
                content_type VARCHAR(100),
                status ENUM('active', 'archived', 'deleted') DEFAULT 'active',
                version INT DEFAULT 1,
                meta_keywords TEXT,
                meta_description TEXT,
                language VARCHAR(10) DEFAULT 'en',
                charset VARCHAR(50) DEFAULT 'UTF-8',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_filename (filename),
                INDEX idx_project_id (project_id),
                INDEX idx_status (status),
                INDEX idx_created_at (created_at),
                INDEX idx_file_hash (file_hash),
                FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE SET NULL
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
            $conn->exec($sql);

            // Enhanced Forms table
            $sql = "CREATE TABLE IF NOT EXISTS forms (
                id INT AUTO_INCREMENT PRIMARY KEY,
                page_id INT NOT NULL,
                form_name VARCHAR(255),
                form_action VARCHAR(500),
                form_method VARCHAR(10) DEFAULT 'GET',
                form_enctype VARCHAR(100),
                form_id VARCHAR(255),
                form_class VARCHAR(255),
                form_target VARCHAR(50),
                form_autocomplete ENUM('on', 'off') DEFAULT 'on',
                form_novalidate BOOLEAN DEFAULT FALSE,
                form_index INT NOT NULL DEFAULT 0,
                form_description TEXT,
                is_active BOOLEAN DEFAULT TRUE,
                validation_rules JSON,
                custom_attributes JSON,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (page_id) REFERENCES pages(id) ON DELETE CASCADE,
                INDEX idx_page_id (page_id),
                INDEX idx_form_name (form_name),
                INDEX idx_is_active (is_active)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
            $conn->exec($sql);

            // Enhanced Form fields table
            $sql = "CREATE TABLE IF NOT EXISTS form_fields (
                id INT AUTO_INCREMENT PRIMARY KEY,
                form_id INT NOT NULL,
                field_name VARCHAR(255) NOT NULL,
                field_type VARCHAR(50) NOT NULL,
                field_id VARCHAR(255),
                field_class VARCHAR(255),
                field_placeholder VARCHAR(500),
                field_value TEXT,
                field_label VARCHAR(500),
                field_required BOOLEAN DEFAULT FALSE,
                field_readonly BOOLEAN DEFAULT FALSE,
                field_disabled BOOLEAN DEFAULT FALSE,
                field_multiple BOOLEAN DEFAULT FALSE,
                field_min VARCHAR(50),
                field_max VARCHAR(50),
                field_step VARCHAR(50),
                field_pattern VARCHAR(500),
                field_maxlength INT,
                field_minlength INT,
                field_size INT,
                field_rows INT,
                field_cols INT,
                field_accept VARCHAR(255),
                field_autocomplete VARCHAR(100),
                field_options JSON, -- JSON for select options, radio buttons, checkboxes
                field_validation_rules JSON,
                field_custom_attributes JSON,
                field_order INT DEFAULT 0,
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (form_id) REFERENCES forms(id) ON DELETE CASCADE,
                INDEX idx_form_id (form_id),
                INDEX idx_field_name (field_name),
                INDEX idx_field_type (field_type),
                INDEX idx_is_active (is_active)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
            $conn->exec($sql);

            // Associated files table (CSS, JS, images, fonts)
            $sql = "CREATE TABLE IF NOT EXISTS associated_files (
                id INT AUTO_INCREMENT PRIMARY KEY,
                page_id INT NOT NULL,
                filename VARCHAR(255) NOT NULL,
                original_filename VARCHAR(255) NOT NULL,
                file_path VARCHAR(500) NOT NULL,
                file_type VARCHAR(50) NOT NULL, -- css, js, image, font
                file_size INT NOT NULL,
                mime_type VARCHAR(100),
                is_referenced BOOLEAN DEFAULT FALSE, -- whether it's referenced in the HTML
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (page_id) REFERENCES pages(id) ON DELETE CASCADE,
                INDEX idx_page_id (page_id),
                INDEX idx_file_type (file_type)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
            $conn->exec($sql);

            // Generated database tables (for dynamic table creation based on forms)
            $sql = "CREATE TABLE IF NOT EXISTS generated_tables (
                id INT AUTO_INCREMENT PRIMARY KEY,
                table_name VARCHAR(255) NOT NULL UNIQUE,
                form_id INT NOT NULL,
                table_sql TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (form_id) REFERENCES forms(id) ON DELETE CASCADE,
                INDEX idx_form_id (form_id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
            $conn->exec($sql);

            // Enhanced Analysis log table
            $sql = "CREATE TABLE IF NOT EXISTS analysis_log (
                id INT AUTO_INCREMENT PRIMARY KEY,
                page_id INT DEFAULT NULL,
                form_id INT DEFAULT NULL,
                analysis_type VARCHAR(50) NOT NULL, -- form_detection, field_analysis, validation, etc.
                status VARCHAR(20) NOT NULL, -- success, error, warning, info
                message TEXT,
                details JSON,
                execution_time DECIMAL(10,4) DEFAULT NULL,
                memory_usage INT DEFAULT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (page_id) REFERENCES pages(id) ON DELETE CASCADE,
                FOREIGN KEY (form_id) REFERENCES forms(id) ON DELETE CASCADE,
                INDEX idx_page_id (page_id),
                INDEX idx_form_id (form_id),
                INDEX idx_analysis_type (analysis_type),
                INDEX idx_status (status),
                INDEX idx_created_at (created_at)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
            $conn->exec($sql);

            // Form templates table (for reusable form structures)
            $sql = "CREATE TABLE IF NOT EXISTS form_templates (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(255) NOT NULL,
                description TEXT,
                category VARCHAR(100) DEFAULT 'general',
                template_data JSON NOT NULL,
                is_system BOOLEAN DEFAULT FALSE,
                is_active BOOLEAN DEFAULT TRUE,
                usage_count INT DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_name (name),
                INDEX idx_category (category),
                INDEX idx_is_active (is_active)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
            $conn->exec($sql);

            // Form submissions table (for storing actual form data)
            $sql = "CREATE TABLE IF NOT EXISTS form_submissions (
                id INT AUTO_INCREMENT PRIMARY KEY,
                form_id INT NOT NULL,
                submission_data JSON NOT NULL,
                ip_address VARCHAR(45),
                user_agent TEXT,
                referrer VARCHAR(500),
                status ENUM('pending', 'processed', 'archived', 'spam') DEFAULT 'pending',
                processed_at TIMESTAMP NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (form_id) REFERENCES forms(id) ON DELETE CASCADE,
                INDEX idx_form_id (form_id),
                INDEX idx_status (status),
                INDEX idx_created_at (created_at)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
            $conn->exec($sql);

            // Page versions table (for version control)
            $sql = "CREATE TABLE IF NOT EXISTS page_versions (
                id INT AUTO_INCREMENT PRIMARY KEY,
                page_id INT NOT NULL,
                version_number INT NOT NULL,
                file_path VARCHAR(500) NOT NULL,
                file_hash VARCHAR(64) NOT NULL,
                changes_summary TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (page_id) REFERENCES pages(id) ON DELETE CASCADE,
                UNIQUE KEY uk_page_version (page_id, version_number),
                INDEX idx_page_id (page_id),
                INDEX idx_version_number (version_number)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
            $conn->exec($sql);

            // Activity log table (for tracking user actions)
            $sql = "CREATE TABLE IF NOT EXISTS activity_log (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT DEFAULT 1,
                action VARCHAR(100) NOT NULL,
                entity_type VARCHAR(50), -- page, form, field, etc.
                entity_id INT,
                old_values JSON,
                new_values JSON,
                ip_address VARCHAR(45),
                user_agent TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
                INDEX idx_user_id (user_id),
                INDEX idx_action (action),
                INDEX idx_entity (entity_type, entity_id),
                INDEX idx_created_at (created_at)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
            $conn->exec($sql);

            return true;
        } catch(PDOException $exception) {
            echo "Table creation error: " . $exception->getMessage();
            return false;
        }
    }

    public function initializeDatabase() {
        if ($this->createDatabase() && $this->createTables() && $this->insertInitialData()) {
            return true;
        }
        return false;
    }

    public function insertInitialData() {
        $conn = $this->getConnection();

        if (!$conn) {
            return false;
        }

        try {
            // Insert default admin user
            $sql = "INSERT IGNORE INTO users (id, username, email, password_hash, full_name, role)
                    VALUES (1, 'admin', 'admin@localhost', '', 'System Administrator', 'admin')";
            $conn->exec($sql);

            // Insert default project
            $sql = "INSERT IGNORE INTO projects (id, name, description, slug, created_by)
                    VALUES (1, 'Default Project', 'Default project for imported pages', 'default', 1)";
            $conn->exec($sql);

            // Insert default application settings
            $settings = [
                ['max_file_size', '10485760', 'integer', 'Maximum file upload size in bytes (10MB)'],
                ['allowed_file_types', 'html,htm,css,js,png,jpg,jpeg,gif,svg,woff,woff2,ttf,otf', 'string', 'Allowed file extensions for upload'],
                ['auto_analyze_forms', '1', 'boolean', 'Automatically analyze forms when pages are imported'],
                ['keep_file_backups', '1', 'boolean', 'Keep backup copies of modified files'],
                ['max_backup_versions', '5', 'integer', 'Maximum number of backup versions to keep'],
                ['enable_form_submissions', '0', 'boolean', 'Enable form submission storage'],
                ['cleanup_old_logs', '1', 'boolean', 'Automatically cleanup old log entries'],
                ['log_retention_days', '30', 'integer', 'Number of days to keep log entries'],
                ['app_version', '2.0.0', 'string', 'Application version'],
                ['database_version', '2.0.0', 'string', 'Database schema version']
            ];

            foreach ($settings as $setting) {
                $sql = "INSERT IGNORE INTO app_settings (setting_key, setting_value, setting_type, description, is_system)
                        VALUES (?, ?, ?, ?, 1)";
                $stmt = $conn->prepare($sql);
                $stmt->execute($setting);
            }

            // Insert some default form templates
            $templates = [
                [
                    'Contact Form',
                    'Basic contact form with name, email, and message fields',
                    'contact',
                    json_encode([
                        'fields' => [
                            ['name' => 'name', 'type' => 'text', 'required' => true, 'label' => 'Full Name'],
                            ['name' => 'email', 'type' => 'email', 'required' => true, 'label' => 'Email Address'],
                            ['name' => 'subject', 'type' => 'text', 'required' => false, 'label' => 'Subject'],
                            ['name' => 'message', 'type' => 'textarea', 'required' => true, 'label' => 'Message']
                        ]
                    ])
                ],
                [
                    'Registration Form',
                    'User registration form with validation',
                    'registration',
                    json_encode([
                        'fields' => [
                            ['name' => 'username', 'type' => 'text', 'required' => true, 'label' => 'Username'],
                            ['name' => 'email', 'type' => 'email', 'required' => true, 'label' => 'Email'],
                            ['name' => 'password', 'type' => 'password', 'required' => true, 'label' => 'Password'],
                            ['name' => 'confirm_password', 'type' => 'password', 'required' => true, 'label' => 'Confirm Password'],
                            ['name' => 'terms', 'type' => 'checkbox', 'required' => true, 'label' => 'I agree to the terms']
                        ]
                    ])
                ]
            ];

            foreach ($templates as $template) {
                $sql = "INSERT IGNORE INTO form_templates (name, description, category, template_data, is_system)
                        VALUES (?, ?, ?, ?, 1)";
                $stmt = $conn->prepare($sql);
                $stmt->execute($template);
            }

            return true;
        } catch(PDOException $exception) {
            echo "Initial data insertion error: " . $exception->getMessage();
            return false;
        }
    }

    public function getVersion() {
        $conn = $this->getConnection();
        if (!$conn) return '1.0.0';

        try {
            $sql = "SELECT setting_value FROM app_settings WHERE setting_key = 'database_version'";
            $stmt = $conn->prepare($sql);
            $stmt->execute();
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            return $result ? $result['setting_value'] : '1.0.0';
        } catch(PDOException $e) {
            return '1.0.0';
        }
    }
}

// Initialize database on first load
$database = new Database();
if (!$database->initializeDatabase()) {
    die("Failed to initialize database. Please check your database configuration.");
}
