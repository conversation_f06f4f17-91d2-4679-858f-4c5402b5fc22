# ZIP File Extraction Process

This document explains how ZIP files are processed and extracted in the Webpage Manager application.

## Overview

The system supports uploading ZIP files containing complete websites with HTML pages and their associated assets (CSS, JS, images, fonts). The extraction process automatically:

1. **Extracts** the ZIP file to a temporary directory
2. **Identifies** HTML files and their referenced assets
3. **Processes** each HTML file as a separate page
4. **Associates** assets with their corresponding pages
5. **Stores** everything in the database with proper relationships
6. **Cleans up** temporary files

## Technical Implementation

### 1. ZIP File Detection

When files are uploaded, the system checks the file extension:

```php
$extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
if ($extension === 'zip') {
    $result = $this->processZipFile($file, $projectId);
} else {
    $result = $this->processIndividualFile($file, $projectId, $uploadType);
}
```

### 2. ZIP Extraction Process

#### Step 1: Create Temporary Directory
```php
$tempDir = $this->uploadDir . 'temp' . DIRECTORY_SEPARATOR . uniqid('zip_') . DIRECTORY_SEPARATOR;
mkdir($tempDir, 0755, true);
```

#### Step 2: Extract ZIP Contents
```php
$zip = new ZipArchive();
$result = $zip->open($file['tmp_name']);

if ($result !== TRUE) {
    return ['success' => false, 'message' => 'Failed to open ZIP file'];
}

$zip->extractTo($tempDir);
$zip->close();
```

#### Step 3: Find HTML Files
```php
$htmlFiles = $this->findHtmlFiles($tempDir);
```

The `findHtmlFiles()` method recursively searches for HTML files:
- Looks for `.html` and `.htm` extensions
- Scans all subdirectories
- Returns full paths to all HTML files found

#### Step 4: Process Each HTML File
```php
foreach ($htmlFiles as $htmlFile) {
    $pageResult = $this->processHtmlWithAssets($htmlFile, $tempDir, $projectId);
    if ($pageResult['success']) {
        $processedPages[] = $pageResult;
    }
}
```

### 3. HTML and Asset Processing

#### Asset Reference Extraction
The system analyzes HTML content to find referenced assets:

```php
private function extractAssetReferences($htmlContent) {
    $references = [];
    
    // CSS files
    preg_match_all('/<link[^>]+href=["\']([^"\']+\.css)["\'][^>]*>/i', $htmlContent, $matches);
    $references = array_merge($references, $matches[1]);
    
    // JavaScript files
    preg_match_all('/<script[^>]+src=["\']([^"\']+\.js)["\'][^>]*>/i', $htmlContent, $matches);
    $references = array_merge($references, $matches[1]);
    
    // Images
    preg_match_all('/<img[^>]+src=["\']([^"\']+)["\'][^>]*>/i', $htmlContent, $matches);
    $references = array_merge($references, $matches[1]);
    
    // Background images in CSS
    preg_match_all('/background-image:\s*url\(["\']?([^"\']+)["\']?\)/i', $htmlContent, $matches);
    $references = array_merge($references, $matches[1]);
    
    return array_unique($references);
}
```

#### Asset File Processing
For each referenced asset:

1. **Locate** the file in the extracted directory
2. **Validate** file type and size
3. **Generate** unique filename
4. **Move** to appropriate directory (`uploads/assets/`)
5. **Store** in database with page association

```php
private function processAssetFile($assetPath, $pageId, $originalRef) {
    // Generate unique filename
    $fileInfo = pathinfo($assetPath);
    $uniqueName = $fileInfo['filename'] . '_' . time() . '_' . uniqid() . '.' . $fileInfo['extension'];
    
    // Determine target directory
    $targetDir = $this->uploadDir . 'assets' . DIRECTORY_SEPARATOR;
    $targetPath = $targetDir . $uniqueName;
    
    // Move file
    if (copy($assetPath, $targetPath)) {
        // Save to database
        $sql = "INSERT INTO associated_files (page_id, filename, original_filename, file_path, file_type, file_size, mime_type, is_referenced) 
                VALUES (?, ?, ?, ?, ?, ?, ?, 1)";
        // ... database insertion
    }
}
```

### 4. Database Storage Structure

#### Pages Table
Each HTML file creates a record in the `pages` table:
- `filename`: Unique generated filename
- `original_filename`: Original filename from ZIP
- `title`: Extracted from HTML `<title>` tag
- `file_path`: Full path to stored HTML file
- `file_size`: File size in bytes
- `file_hash`: SHA256 hash for integrity
- `content_type`: MIME type (text/html)

#### Associated Files Table
Each asset creates a record in the `associated_files` table:
- `page_id`: Links to the parent HTML page
- `filename`: Unique generated filename
- `original_filename`: Original filename from ZIP
- `file_path`: Full path to stored asset
- `file_type`: Category (css, js, image, font)
- `file_size`: File size in bytes
- `mime_type`: MIME type
- `is_referenced`: Whether it's referenced in HTML

### 5. File Organization

After extraction, files are organized as follows:

```
uploads/
├── pages/
│   ├── homepage_1234567890_abc123.html
│   ├── about_1234567891_def456.html
│   └── contact_1234567892_ghi789.html
├── assets/
│   ├── style_1234567893_jkl012.css
│   ├── script_1234567894_mno345.js
│   ├── logo_1234567895_pqr678.png
│   └── font_1234567896_stu901.woff
└── temp/
    └── zip_abc123def456/  (cleaned up after processing)
```

### 6. Form Analysis

After HTML processing, the system automatically analyzes forms:

```php
if ($fileType === 'html') {
    $this->analyzeHTMLFile($pageId, $targetPath);
    $this->extractAndSaveMetadata($pageId, $targetPath);
}
```

This creates records in:
- `forms` table: One record per `<form>` element
- `form_fields` table: One record per input/textarea/select element

### 7. Cleanup Process

After successful processing:

```php
$this->removeDirectory($tempDir);
```

The temporary extraction directory is completely removed to save disk space.

## Supported File Types

### HTML Files
- `.html`, `.htm`
- Processed as main pages
- Analyzed for forms and metadata

### CSS Files
- `.css`
- Stored as associated assets
- Linked via `assets.php` for proper serving

### JavaScript Files
- `.js`
- Stored as associated assets
- Maintains original functionality

### Images
- `.png`, `.jpg`, `.jpeg`, `.gif`, `.svg`, `.webp`
- Stored as associated assets
- Properly linked in HTML

### Fonts
- `.woff`, `.woff2`, `.ttf`, `.otf`, `.eot`
- Stored as associated assets
- CSS font references updated

### Other Assets
- Any file referenced in HTML/CSS
- Stored with proper MIME type detection

## Error Handling

The system handles various error conditions:

### ZIP File Errors
- Corrupted ZIP files
- Password-protected ZIPs
- Unsupported compression methods
- Memory limitations

### File System Errors
- Insufficient disk space
- Permission issues
- Path length limitations
- Invalid filenames

### Processing Errors
- Malformed HTML
- Missing referenced assets
- Database connection issues
- Timeout on large files

## Performance Considerations

### Memory Usage
- Large ZIP files are processed in chunks
- Temporary files are cleaned up immediately
- Database transactions prevent partial imports

### Processing Time
- Asynchronous processing for large files
- Progress tracking for user feedback
- Timeout protection for web requests

### Storage Efficiency
- Duplicate detection prevents redundant storage
- File compression for text assets
- Automatic cleanup of orphaned files

## Usage Examples

### Simple Website ZIP
```
website.zip
├── index.html
├── about.html
├── css/
│   └── style.css
├── js/
│   └── script.js
└── images/
    ├── logo.png
    └── banner.jpg
```

Results in:
- 2 pages in database
- 4 associated files
- All references properly linked

### Complex Multi-page Site
```
portfolio.zip
├── index.html
├── portfolio/
│   ├── project1.html
│   └── project2.html
├── assets/
│   ├── css/
│   │   ├── main.css
│   │   └── responsive.css
│   ├── js/
│   │   ├── jquery.min.js
│   │   └── app.js
│   └── images/
│       ├── hero.jpg
│       └── gallery/
│           ├── img1.jpg
│           └── img2.jpg
└── fonts/
    ├── roboto.woff2
    └── opensans.woff2
```

Results in:
- 3 pages in database
- 8 associated files
- Proper subdirectory handling
- All cross-references maintained

This comprehensive extraction system ensures that complete websites can be uploaded as ZIP files and function exactly as they did originally, with all assets properly linked and forms ready for data collection.
