<?php
/**
 * Test Form Submission Handler
 * Direct test of the submit_form.php functionality
 */

echo "<h1>Form Submission Handler Test</h1>";

// Test form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    echo "<h2>Processing Form Submission</h2>";
    
    try {
        require_once 'config/database.php';
        
        $database = new Database();
        $db = $database->getConnection();
        
        if (!$db) {
            throw new Exception("Database connection failed");
        }
        
        echo "<p>✅ Database connection successful</p>";
        
        $shareToken = $_POST['_share_token'] ?? null;
        $formId = $_POST['_form_id'] ?? null;
        $pageId = $_POST['_page_id'] ?? null;
        
        echo "<p><strong>Share Token:</strong> " . htmlspecialchars($shareToken ?: 'None') . "</p>";
        echo "<p><strong>Form ID:</strong> " . htmlspecialchars($formId ?: 'None') . "</p>";
        echo "<p><strong>Page ID:</strong> " . htmlspecialchars($pageId ?: 'None') . "</p>";
        
        // Validate share token if provided
        $shareInfo = null;
        if ($shareToken) {
            echo "<h3>Validating Share Token</h3>";
            
            // Check if page_shares table exists and has correct structure
            $sql = "SHOW TABLES LIKE 'page_shares'";
            $stmt = $db->prepare($sql);
            $stmt->execute();
            $tableExists = $stmt->fetch();
            
            if (!$tableExists) {
                throw new Exception("page_shares table does not exist");
            }
            
            echo "<p>✅ page_shares table exists</p>";
            
            // Check table structure
            $sql = "DESCRIBE page_shares";
            $stmt = $db->prepare($sql);
            $stmt->execute();
            $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
            
            echo "<p><strong>page_shares columns:</strong> " . implode(', ', $columns) . "</p>";
            
            if (!in_array('page_id', $columns)) {
                throw new Exception("page_id column missing from page_shares table");
            }
            
            echo "<p>✅ page_id column exists</p>";
            
            // Try the problematic query
            $sql = "SELECT ps.*, p.id as page_id, p.title as page_title 
                    FROM page_shares ps 
                    JOIN pages p ON ps.page_id = p.id 
                    WHERE ps.share_token = ? AND ps.is_active = 1";
            $stmt = $db->prepare($sql);
            $stmt->execute([$shareToken]);
            $shareInfo = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($shareInfo) {
                echo "<p>✅ Share found: {$shareInfo['title']}</p>";
                $pageId = $shareInfo['page_id'];
            } else {
                echo "<p>❌ Share not found with token: $shareToken</p>";
                
                // Show available shares
                $sql = "SELECT share_token, title FROM page_shares WHERE is_active = 1 LIMIT 5";
                $stmt = $db->prepare($sql);
                $stmt->execute();
                $shares = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                if (count($shares) > 0) {
                    echo "<p><strong>Available shares:</strong></p>";
                    echo "<ul>";
                    foreach ($shares as $share) {
                        echo "<li>{$share['title']} (Token: {$share['share_token']})</li>";
                    }
                    echo "</ul>";
                } else {
                    echo "<p>No shares available</p>";
                }
            }
        }
        
        if ($pageId) {
            echo "<h3>Processing Form Data</h3>";
            
            // Prepare form data (exclude system fields)
            $formData = $_POST;
            $systemFields = ['_share_token', '_form_id', '_page_id', '_form_name'];
            foreach ($systemFields as $field) {
                unset($formData[$field]);
            }
            
            echo "<p><strong>Form data:</strong></p>";
            echo "<pre>" . print_r($formData, true) . "</pre>";
            
            // Get visitor information
            $ipAddress = $_SERVER['REMOTE_ADDR'] ?? '';
            $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
            $referrer = $_SERVER['HTTP_REFERER'] ?? '';
            
            // Store form submission
            $sql = "INSERT INTO form_submissions (
                        page_id, form_id, share_id, form_data, ip_address, 
                        user_agent, referrer, submitted_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, NOW())";
            
            $stmt = $db->prepare($sql);
            $stmt->execute([
                $pageId,
                $formId,
                $shareInfo['id'] ?? null,
                json_encode($formData),
                $ipAddress,
                $userAgent,
                $referrer
            ]);
            
            $submissionId = $db->lastInsertId();
            
            echo "<p>✅ Form submission stored with ID: $submissionId</p>";
            
            // Show success message
            echo "<div style='background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
            echo "<h3>✅ Form Submitted Successfully!</h3>";
            echo "<p>Your form has been submitted and stored in the database.</p>";
            echo "<p><strong>Submission ID:</strong> $submissionId</p>";
            echo "</div>";
            
        } else {
            throw new Exception("No valid page ID found");
        }
        
    } catch (Exception $e) {
        echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h3>❌ Error Processing Form</h3>";
        echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
        echo "</div>";
    }
    
} else {
    // Show test form
    echo "<h2>Test Form Submission</h2>";
    
    try {
        require_once 'config/database.php';
        
        $database = new Database();
        $db = $database->getConnection();
        
        // Get a test share token
        $sql = "SELECT share_token, title, page_id FROM page_shares WHERE is_active = 1 LIMIT 1";
        $stmt = $db->prepare($sql);
        $stmt->execute();
        $testShare = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($testShare) {
            echo "<p>Using test share: <strong>{$testShare['title']}</strong></p>";
            echo "<p>Share token: <code>{$testShare['share_token']}</code></p>";
            
            echo '<form method="POST" action="">
                <input type="hidden" name="_share_token" value="' . htmlspecialchars($testShare['share_token']) . '">
                <input type="hidden" name="_page_id" value="' . htmlspecialchars($testShare['page_id']) . '">
                <input type="hidden" name="_form_name" value="test_form">
                
                <div style="margin-bottom: 15px;">
                    <label>Name:</label>
                    <input type="text" name="name" value="Test User" required style="width: 100%; padding: 8px;">
                </div>
                
                <div style="margin-bottom: 15px;">
                    <label>Email:</label>
                    <input type="email" name="email" value="<EMAIL>" required style="width: 100%; padding: 8px;">
                </div>
                
                <div style="margin-bottom: 15px;">
                    <label>Message:</label>
                    <textarea name="message" required style="width: 100%; padding: 8px; height: 100px;">This is a test form submission to verify the form data collection system is working correctly.</textarea>
                </div>
                
                <button type="submit" style="background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer;">
                    Submit Test Form
                </button>
            </form>';
            
        } else {
            echo "<p>❌ No test shares available. Please create a share first using the main application.</p>";
        }
        
    } catch (Exception $e) {
        echo "<p>❌ Error: " . $e->getMessage() . "</p>";
    }
}

echo "<h2>Quick Links</h2>";
echo "<p><a href='create_database_tables.php'>Create Database Tables</a></p>";
echo "<p><a href='fix_database_structure.php'>Fix Database Structure</a></p>";
echo "<p><a href='test_form_submission.php'>Form Submission System Test</a></p>";
echo "<p><a href='index.html'>Main Application</a></p>";

?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
form { background: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0; }
label { display: block; margin-bottom: 5px; font-weight: bold; }
input, textarea { margin-bottom: 10px; }
pre { background: #f5f5f5; padding: 10px; border-radius: 5px; overflow-x: auto; }
code { background: #f5f5f5; padding: 2px 4px; border-radius: 3px; }
</style>
