<?php
/**
 * Quick Test - Actual Results Verification
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Quick Test - Actual Results Verification</h1>";

echo "<h2>1. Direct API Test</h2>";

// Test the sharing manager directly
try {
    require_once 'includes/sharing_manager.php';
    
    $manager = new SharingManager();
    $result = $manager->getFormSubmissions();
    
    echo "<h3>API Response:</h3>";
    echo "<pre style='background: #f5f5f5; padding: 10px; border-radius: 5px;'>";
    print_r($result);
    echo "</pre>";
    
    if ($result['success']) {
        echo "<div style='background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h3>✅ API Working</h3>";
        echo "<p><strong>Total submissions:</strong> {$result['total']}</p>";
        echo "<p><strong>Submissions returned:</strong> " . count($result['submissions']) . "</p>";
        
        if (!empty($result['submissions'])) {
            echo "<h4>Testing Data Preview for First Submission:</h4>";
            $sample = $result['submissions'][0];
            
            echo "<p><strong>Submission ID:</strong> {$sample['id']}</p>";
            echo "<p><strong>Raw form_data:</strong> " . htmlspecialchars($sample['form_data'] ?? 'NULL') . "</p>";
            
            // Test the preview formatting logic
            $formData = $sample['form_data'] ?? null;
            
            if (!$formData || $formData === 'null' || $formData === '') {
                $preview = 'No data';
                echo "<p style='color: orange;'>⚠️ Preview: $preview</p>";
            } else {
                try {
                    $data = json_decode($formData, true);
                    if (json_last_error() === JSON_ERROR_NONE) {
                        if (!empty($data) && is_array($data) && count($data) > 0) {
                            $fields = array_keys($data);
                            $previewFields = array_slice($fields, 0, 3);
                            $preview = implode(', ', array_map(function($field) use ($data) {
                                $value = $data[$field];
                                $displayValue = strlen($value) > 20 ? substr($value, 0, 20) . '...' : $value;
                                return "$field: $displayValue";
                            }, $previewFields));
                            
                            if (strlen($preview) > 50) {
                                $preview = substr($preview, 0, 50) . '...';
                            }
                            
                            echo "<p style='color: green;'>✅ Preview: $preview</p>";
                        } else {
                            echo "<p style='color: orange;'>⚠️ Preview: Empty form (valid JSON but no data)</p>";
                        }
                    } else {
                        echo "<p style='color: red;'>❌ Preview: Invalid data format (JSON error: " . json_last_error_msg() . ")</p>";
                    }
                } catch (Exception $e) {
                    echo "<p style='color: red;'>❌ Preview: Invalid data format (Exception: " . $e->getMessage() . ")</p>";
                }
            }
            
            echo "<h4>Full Sample Data:</h4>";
            echo "<pre style='background: #e9ecef; padding: 10px; border-radius: 5px;'>";
            print_r($sample);
            echo "</pre>";
        } else {
            echo "<p style='color: orange;'>⚠️ No submissions found</p>";
        }
        echo "</div>";
    } else {
        echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h3>❌ API Error</h3>";
        echo "<p>{$result['message']}</p>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>❌ Exception</h3>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
    echo "</div>";
}

echo "<h2>2. Database Direct Check</h2>";

try {
    require_once 'config/database.php';
    $database = new Database();
    $db = $database->getConnection();
    
    // Check if we have any submissions
    $sql = "SELECT COUNT(*) as total FROM form_submissions";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $count = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo "<p><strong>Total submissions in database:</strong> {$count['total']}</p>";
    
    if ($count['total'] > 0) {
        // Get column info
        $sql = "DESCRIBE form_submissions";
        $stmt = $db->prepare($sql);
        $stmt->execute();
        $columns = array_column($stmt->fetchAll(PDO::FETCH_ASSOC), 'Field');
        
        $dataColumn = 'submission_data';
        if (in_array('form_data', $columns) && !in_array('submission_data', $columns)) {
            $dataColumn = 'form_data';
        }
        
        echo "<p><strong>Data column being used:</strong> $dataColumn</p>";
        
        // Get sample data
        $sql = "SELECT id, $dataColumn as data_content, submitted_at FROM form_submissions ORDER BY submitted_at DESC LIMIT 3";
        $stmt = $db->prepare($sql);
        $stmt->execute();
        $samples = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<h3>Sample Database Records:</h3>";
        foreach ($samples as $sample) {
            echo "<div style='background: #f8f9fa; padding: 10px; margin: 10px 0; border-radius: 5px;'>";
            echo "<h4>ID: {$sample['id']}</h4>";
            echo "<p><strong>Raw Data:</strong> " . htmlspecialchars($sample['data_content'] ?? 'NULL') . "</p>";
            echo "<p><strong>Date:</strong> {$sample['submitted_at']}</p>";
            
            // Test parsing
            if ($sample['data_content']) {
                try {
                    $parsed = json_decode($sample['data_content'], true);
                    if (json_last_error() === JSON_ERROR_NONE) {
                        echo "<p style='color: green;'>✅ Valid JSON</p>";
                        echo "<pre style='background: #d4edda; padding: 10px; border-radius: 3px;'>";
                        echo htmlspecialchars(json_encode($parsed, JSON_PRETTY_PRINT));
                        echo "</pre>";
                    } else {
                        echo "<p style='color: red;'>❌ Invalid JSON: " . json_last_error_msg() . "</p>";
                    }
                } catch (Exception $e) {
                    echo "<p style='color: red;'>❌ Parse error: " . $e->getMessage() . "</p>";
                }
            } else {
                echo "<p style='color: orange;'>⚠️ No data</p>";
            }
            echo "</div>";
        }
    } else {
        echo "<div style='background: #fff3cd; color: #856404; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h3>⚠️ No Data Found</h3>";
        echo "<p>No submissions found in database. Creating test data...</p>";
        echo "</div>";
        
        // Create test data
        $testData = [
            ['name' => 'John Doe', 'email' => '<EMAIL>', 'message' => 'Test message 1'],
            ['name' => 'Jane Smith', 'email' => '<EMAIL>', 'message' => 'Test message 2'],
            ['rating' => '5', 'feedback' => 'Great service!', 'recommend' => 'yes']
        ];
        
        $dataColumn = in_array('submission_data', $columns) ? 'submission_data' : 'form_data';
        
        foreach ($testData as $data) {
            $sql = "INSERT INTO form_submissions (page_id, $dataColumn, ip_address, submitted_at) VALUES (?, ?, ?, NOW())";
            $stmt = $db->prepare($sql);
            $stmt->execute([1, json_encode($data), '127.0.0.1']);
        }
        
        echo "<p>✅ Created " . count($testData) . " test submissions</p>";
        echo "<p><a href='quick_test_actual_results.php' style='background: #007cba; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Refresh to see results</a></p>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>❌ Database Error</h3>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "<h2>3. Frontend Test</h2>";

echo "<div style='background: #e7f3ff; color: #004085; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h3>JavaScript Test</h3>";
echo "<p>Click the button below to test the frontend API call:</p>";
echo "<button onclick='testFrontendAPI()' style='background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;'>Test Frontend API</button>";
echo "<div id='frontendResult' style='margin: 15px 0;'></div>";
echo "</div>";

echo "<h2>4. Manual Verification Steps</h2>";

echo "<div style='background: #fff3cd; color: #856404; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h3>⚠️ Please Verify Manually</h3>";
echo "<ol>";
echo "<li><strong>Open Main App:</strong> <a href='index.html#database' target='_blank' style='color: #007cba;'>Click here to open Database tab</a></li>";
echo "<li><strong>Check Data Display:</strong> Look for actual form data instead of 'Invalid data'</li>";
echo "<li><strong>Check Delete Buttons:</strong> Look for trash can icons in the Actions column</li>";
echo "<li><strong>Test Delete:</strong> Try clicking a delete button</li>";
echo "</ol>";
echo "</div>";

echo "<h2>5. Summary</h2>";

echo "<div id='testSummary'>";
echo "<p>Run the tests above to see actual results...</p>";
echo "</div>";

?>

<script>
async function testFrontendAPI() {
    const resultDiv = document.getElementById('frontendResult');
    resultDiv.innerHTML = '<p style="color: #007cba;">Testing frontend API...</p>';
    
    try {
        const formData = new FormData();
        formData.append('action', 'get_submissions');
        
        const response = await fetch('includes/sharing_manager.php', {
            method: 'POST',
            body: formData
        });
        
        console.log('Response status:', response.status);
        console.log('Response headers:', Object.fromEntries(response.headers.entries()));
        
        const text = await response.text();
        console.log('Raw response:', text);
        
        try {
            const data = JSON.parse(text);
            console.log('Parsed data:', data);
            
            if (data.success) {
                let html = `
                    <div style="background: #d4edda; color: #155724; padding: 15px; border-radius: 5px;">
                        <h4>✅ Frontend API Test Successful</h4>
                        <p><strong>Total submissions:</strong> ${data.total}</p>
                        <p><strong>Submissions loaded:</strong> ${data.submissions.length}</p>
                `;
                
                if (data.submissions.length > 0) {
                    const sample = data.submissions[0];
                    html += `<p><strong>Sample ID:</strong> ${sample.id}</p>`;
                    
                    // Test preview formatting
                    const formDataContent = sample.form_data;
                    html += `<p><strong>Raw form_data:</strong> ${formDataContent}</p>`;
                    
                    try {
                        if (!formDataContent || formDataContent === 'null' || formDataContent === '') {
                            html += `<p><strong>Preview:</strong> No data</p>`;
                        } else {
                            const parsed = JSON.parse(formDataContent);
                            if (parsed && typeof parsed === 'object' && Object.keys(parsed).length > 0) {
                                const fields = Object.keys(parsed).slice(0, 3);
                                const preview = fields.map(field => `${field}: ${parsed[field]}`).join(', ');
                                html += `<p><strong>✅ Preview:</strong> ${preview}</p>`;
                            } else {
                                html += `<p><strong>⚠️ Preview:</strong> Empty form</p>`;
                            }
                        }
                    } catch (e) {
                        html += `<p><strong>❌ Preview error:</strong> ${e.message}</p>`;
                    }
                }
                
                html += '</div>';
                resultDiv.innerHTML = html;
                
                updateSummary('✅ Frontend API working correctly');
            } else {
                resultDiv.innerHTML = `
                    <div style="background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px;">
                        <h4>❌ Frontend API Error</h4>
                        <p>${data.message}</p>
                    </div>
                `;
                updateSummary('❌ Frontend API error: ' + data.message);
            }
        } catch (parseError) {
            resultDiv.innerHTML = `
                <div style="background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px;">
                    <h4>❌ JSON Parse Error</h4>
                    <p>${parseError.message}</p>
                    <p><strong>Raw response:</strong> ${text}</p>
                </div>
            `;
            updateSummary('❌ JSON parse error: ' + parseError.message);
        }
    } catch (error) {
        resultDiv.innerHTML = `
            <div style="background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px;">
                <h4>❌ Network Error</h4>
                <p>${error.message}</p>
            </div>
        `;
        updateSummary('❌ Network error: ' + error.message);
    }
}

function updateSummary(message) {
    const summaryDiv = document.getElementById('testSummary');
    const timestamp = new Date().toLocaleTimeString();
    summaryDiv.innerHTML += `<p><strong>[${timestamp}]</strong> ${message}</p>`;
}
</script>

<style>
body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
h1, h2, h3, h4 { color: #333; }
pre { background: #f5f5f5; padding: 10px; border-radius: 5px; overflow-x: auto; }
</style>
