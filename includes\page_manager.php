<?php
/**
 * Page Manager for Webpage Manager v2.0
 * Handles page operations including deletion, archiving, and management
 */

require_once '../config/database.php';

header('Content-Type: application/json');

class PageManager {
    private $db;
    private $uploadDir;
    
    public function __construct() {
        $database = new Database();
        $this->db = $database->getConnection();
        $this->uploadDir = dirname(__DIR__) . DIRECTORY_SEPARATOR . 'uploads' . DIRECTORY_SEPARATOR;
    }
    
    public function deletePage($pageId, $deleteFiles = true) {
        try {
            // Start transaction
            $this->db->beginTransaction();
            
            // Get page information
            $pageInfo = $this->getPageInfo($pageId);
            if (!$pageInfo) {
                throw new Exception('Page not found');
            }
            
            // Get associated files
            $associatedFiles = $this->getAssociatedFiles($pageId);
            
            // Get shares for this page
            $shares = $this->getPageShares($pageId);
            
            // Create backup record before deletion
            $this->createDeletionBackup($pageInfo, $associatedFiles, $shares);
            
            if ($deleteFiles) {
                // Delete physical files
                $this->deletePhysicalFiles($pageInfo, $associatedFiles);
            } else {
                // Just mark as deleted in database
                $this->markAsDeleted($pageId);
            }
            
            // Delete database records (CASCADE will handle related records)
            $sql = "DELETE FROM pages WHERE id = ?";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$pageId]);
            
            // Log the deletion
            $this->logActivity('page_deleted', 'page', $pageId, [
                'page_title' => $pageInfo['title'],
                'original_filename' => $pageInfo['original_filename'],
                'files_deleted' => $deleteFiles,
                'associated_files_count' => count($associatedFiles),
                'shares_count' => count($shares)
            ]);
            
            $this->db->commit();
            
            return [
                'success' => true,
                'message' => 'Page deleted successfully',
                'deleted_files' => $deleteFiles ? count($associatedFiles) + 1 : 0,
                'affected_shares' => count($shares)
            ];
            
        } catch (Exception $e) {
            $this->db->rollback();
            return ['success' => false, 'message' => 'Deletion failed: ' . $e->getMessage()];
        }
    }
    
    public function deleteMultiplePages($pageIds, $deleteFiles = true) {
        $results = [];
        $successCount = 0;
        $errorCount = 0;
        
        foreach ($pageIds as $pageId) {
            $result = $this->deletePage($pageId, $deleteFiles);
            $results[] = [
                'page_id' => $pageId,
                'success' => $result['success'],
                'message' => $result['message']
            ];
            
            if ($result['success']) {
                $successCount++;
            } else {
                $errorCount++;
            }
        }
        
        return [
            'success' => $errorCount === 0,
            'message' => "Deleted {$successCount} pages successfully" . ($errorCount > 0 ? ", {$errorCount} failed" : ""),
            'results' => $results,
            'success_count' => $successCount,
            'error_count' => $errorCount
        ];
    }
    
    public function archivePage($pageId) {
        try {
            $sql = "UPDATE pages SET status = 'archived' WHERE id = ?";
            $stmt = $this->db->prepare($sql);
            $result = $stmt->execute([$pageId]);
            
            if ($result) {
                $this->logActivity('page_archived', 'page', $pageId);
                return ['success' => true, 'message' => 'Page archived successfully'];
            } else {
                return ['success' => false, 'message' => 'Failed to archive page'];
            }
            
        } catch (PDOException $e) {
            return ['success' => false, 'message' => 'Database error: ' . $e->getMessage()];
        }
    }
    
    public function restorePage($pageId) {
        try {
            $sql = "UPDATE pages SET status = 'active' WHERE id = ?";
            $stmt = $this->db->prepare($sql);
            $result = $stmt->execute([$pageId]);
            
            if ($result) {
                $this->logActivity('page_restored', 'page', $pageId);
                return ['success' => true, 'message' => 'Page restored successfully'];
            } else {
                return ['success' => false, 'message' => 'Failed to restore page'];
            }
            
        } catch (PDOException $e) {
            return ['success' => false, 'message' => 'Database error: ' . $e->getMessage()];
        }
    }
    
    public function getPages($status = 'active', $projectId = null) {
        try {
            $sql = "SELECT 
                        p.*,
                        pr.name as project_name,
                        pr.color as project_color,
                        COUNT(DISTINCT f.id) as form_count,
                        COUNT(DISTINCT af.id) as asset_count,
                        COUNT(DISTINCT ps.id) as share_count
                    FROM pages p
                    LEFT JOIN projects pr ON p.project_id = pr.id
                    LEFT JOIN forms f ON p.id = f.page_id AND f.is_active = 1
                    LEFT JOIN associated_files af ON p.id = af.page_id
                    LEFT JOIN page_shares ps ON p.id = ps.page_id AND ps.is_active = 1
                    WHERE p.status = ?";
            
            $params = [$status];
            
            if ($projectId !== null) {
                $sql .= " AND p.project_id = ?";
                $params[] = $projectId;
            }
            
            $sql .= " GROUP BY p.id ORDER BY p.created_at DESC";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);
            $pages = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            return ['success' => true, 'pages' => $pages];
            
        } catch (PDOException $e) {
            return ['success' => false, 'message' => 'Database error: ' . $e->getMessage()];
        }
    }
    
    public function getDeletedPages() {
        try {
            $sql = "SELECT * FROM deletion_backups ORDER BY deleted_at DESC";
            $stmt = $this->db->prepare($sql);
            $stmt->execute();
            $deletedPages = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            return ['success' => true, 'deleted_pages' => $deletedPages];
            
        } catch (PDOException $e) {
            return ['success' => false, 'message' => 'Database error: ' . $e->getMessage()];
        }
    }
    
    public function cleanupOrphanedFiles() {
        try {
            $cleanedFiles = [];
            
            // Find orphaned associated files
            $sql = "SELECT af.* FROM associated_files af 
                    LEFT JOIN pages p ON af.page_id = p.id 
                    WHERE p.id IS NULL";
            $stmt = $this->db->prepare($sql);
            $stmt->execute();
            $orphanedFiles = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            foreach ($orphanedFiles as $file) {
                if (file_exists($file['file_path'])) {
                    unlink($file['file_path']);
                    $cleanedFiles[] = $file['original_filename'];
                }
            }
            
            // Delete orphaned file records
            $sql = "DELETE af FROM associated_files af 
                    LEFT JOIN pages p ON af.page_id = p.id 
                    WHERE p.id IS NULL";
            $stmt = $this->db->prepare($sql);
            $stmt->execute();
            
            $this->logActivity('cleanup_orphaned_files', 'system', null, [
                'files_cleaned' => count($cleanedFiles),
                'file_list' => $cleanedFiles
            ]);
            
            return [
                'success' => true,
                'message' => 'Cleanup completed',
                'files_cleaned' => count($cleanedFiles),
                'cleaned_files' => $cleanedFiles
            ];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'Cleanup failed: ' . $e->getMessage()];
        }
    }
    
    private function getPageInfo($pageId) {
        $sql = "SELECT * FROM pages WHERE id = ?";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$pageId]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    private function getAssociatedFiles($pageId) {
        $sql = "SELECT * FROM associated_files WHERE page_id = ?";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$pageId]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    private function getPageShares($pageId) {
        $sql = "SELECT * FROM page_shares WHERE page_id = ?";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$pageId]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    private function createDeletionBackup($pageInfo, $associatedFiles, $shares) {
        // Create deletion_backups table if it doesn't exist
        $sql = "CREATE TABLE IF NOT EXISTS deletion_backups (
            id INT AUTO_INCREMENT PRIMARY KEY,
            original_page_id INT NOT NULL,
            page_data JSON NOT NULL,
            associated_files_data JSON,
            shares_data JSON,
            deleted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            deleted_by INT DEFAULT 1,
            can_restore BOOLEAN DEFAULT FALSE,
            INDEX idx_original_page_id (original_page_id),
            INDEX idx_deleted_at (deleted_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        $this->db->exec($sql);
        
        // Insert backup record
        $sql = "INSERT INTO deletion_backups (original_page_id, page_data, associated_files_data, shares_data, can_restore) 
                VALUES (?, ?, ?, ?, ?)";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([
            $pageInfo['id'],
            json_encode($pageInfo),
            json_encode($associatedFiles),
            json_encode($shares),
            !file_exists($pageInfo['file_path']) ? 0 : 1 // Can only restore if files still exist
        ]);
    }
    
    private function deletePhysicalFiles($pageInfo, $associatedFiles) {
        // Delete main page file
        if (file_exists($pageInfo['file_path'])) {
            unlink($pageInfo['file_path']);
        }
        
        // Delete associated files
        foreach ($associatedFiles as $file) {
            if (file_exists($file['file_path'])) {
                unlink($file['file_path']);
            }
        }
    }
    
    private function markAsDeleted($pageId) {
        $sql = "UPDATE pages SET status = 'deleted' WHERE id = ?";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$pageId]);
    }
    
    private function logActivity($action, $entityType, $entityId, $data = []) {
        try {
            $sql = "INSERT INTO activity_log (user_id, action, entity_type, entity_id, new_values, ip_address, user_agent) 
                    VALUES (?, ?, ?, ?, ?, ?, ?)";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute([
                1, // Default user ID
                $action,
                $entityType,
                $entityId,
                json_encode($data),
                $_SERVER['REMOTE_ADDR'] ?? '',
                $_SERVER['HTTP_USER_AGENT'] ?? ''
            ]);
        } catch (PDOException $e) {
            // Log errors silently
            error_log("Activity logging failed: " . $e->getMessage());
        }
    }
}

// Handle the request
try {
    $manager = new PageManager();
    $action = $_POST['action'] ?? $_GET['action'] ?? '';
    
    switch ($action) {
        case 'delete_page':
            $pageId = $_POST['page_id'] ?? 0;
            $deleteFiles = $_POST['delete_files'] ?? true;
            $result = $manager->deletePage($pageId, $deleteFiles);
            break;
            
        case 'delete_multiple':
            $pageIds = $_POST['page_ids'] ?? [];
            $deleteFiles = $_POST['delete_files'] ?? true;
            $result = $manager->deleteMultiplePages($pageIds, $deleteFiles);
            break;
            
        case 'archive_page':
            $pageId = $_POST['page_id'] ?? 0;
            $result = $manager->archivePage($pageId);
            break;
            
        case 'restore_page':
            $pageId = $_POST['page_id'] ?? 0;
            $result = $manager->restorePage($pageId);
            break;
            
        case 'get_pages':
            $status = $_GET['status'] ?? 'active';
            $projectId = $_GET['project_id'] ?? null;
            $result = $manager->getPages($status, $projectId);
            break;
            
        case 'get_deleted_pages':
            $result = $manager->getDeletedPages();
            break;
            
        case 'cleanup_orphaned':
            $result = $manager->cleanupOrphanedFiles();
            break;
            
        default:
            $result = ['success' => false, 'message' => 'Invalid action'];
    }
    
    echo json_encode($result);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Server error: ' . $e->getMessage()
    ]);
}
