<?php
/**
 * Get Form Submissions
 * Retrieves form submissions with filtering and pagination
 */

require_once '../config/database.php';

header('Content-Type: application/json');

try {
    $database = new Database();
    $db = $database->getConnection();

    // Check if form_submissions table exists, create if not
    $sql = "CREATE TABLE IF NOT EXISTS form_submissions (
        id INT AUTO_INCREMENT PRIMARY KEY,
        page_id INT NOT NULL,
        form_id INT NULL,
        share_id INT NULL,
        form_data JSON NOT NULL,
        ip_address VARCHAR(45),
        user_agent TEXT,
        referrer TEXT,
        submitted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        processed BOOLEAN DEFAULT FALSE,
        notes TEXT,
        INDEX idx_page_id (page_id),
        INDEX idx_form_id (form_id),
        INDEX idx_share_id (share_id),
        INDEX idx_submitted_at (submitted_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $db->exec($sql);

    // Get specific submission by ID
    if (isset($_GET['id'])) {
        $submissionId = $_GET['id'];
        
        $sql = "SELECT 
                    fs.*,
                    p.title as page_title,
                    p.original_filename as page_filename,
                    f.form_name,
                    ps.title as share_title
                FROM form_submissions fs
                LEFT JOIN pages p ON fs.page_id = p.id
                LEFT JOIN forms f ON fs.form_id = f.id
                LEFT JOIN page_shares ps ON fs.share_id = ps.id
                WHERE fs.id = ?";
        
        $stmt = $db->prepare($sql);
        $stmt->execute([$submissionId]);
        $submission = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($submission) {
            echo json_encode([
                'success' => true,
                'submission' => $submission
            ]);
        } else {
            echo json_encode([
                'success' => false,
                'message' => 'Submission not found'
            ]);
        }
        exit;
    }

    // Build query with filters
    $whereConditions = [];
    $params = [];

    if (isset($_GET['page_id']) && $_GET['page_id']) {
        $whereConditions[] = "fs.page_id = ?";
        $params[] = $_GET['page_id'];
    }

    if (isset($_GET['form_id']) && $_GET['form_id']) {
        $whereConditions[] = "fs.form_id = ?";
        $params[] = $_GET['form_id'];
    }

    if (isset($_GET['date_from']) && $_GET['date_from']) {
        $whereConditions[] = "DATE(fs.submitted_at) >= ?";
        $params[] = $_GET['date_from'];
    }

    if (isset($_GET['date_to']) && $_GET['date_to']) {
        $whereConditions[] = "DATE(fs.submitted_at) <= ?";
        $params[] = $_GET['date_to'];
    }

    $whereClause = '';
    if (!empty($whereConditions)) {
        $whereClause = 'WHERE ' . implode(' AND ', $whereConditions);
    }

    // Get submissions
    $sql = "SELECT 
                fs.*,
                p.title as page_title,
                p.original_filename as page_filename,
                f.form_name,
                ps.title as share_title
            FROM form_submissions fs
            LEFT JOIN pages p ON fs.page_id = p.id
            LEFT JOIN forms f ON fs.form_id = f.id
            LEFT JOIN page_shares ps ON fs.share_id = ps.id
            $whereClause
            ORDER BY fs.submitted_at DESC
            LIMIT 100";

    $stmt = $db->prepare($sql);
    $stmt->execute($params);
    $submissions = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Get pages for filter dropdown
    $sql = "SELECT DISTINCT p.id, p.title, p.original_filename as filename
            FROM pages p
            INNER JOIN form_submissions fs ON p.id = fs.page_id
            ORDER BY p.title, p.original_filename";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $pages = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Get forms for filter dropdown
    $sql = "SELECT DISTINCT f.id, f.form_name
            FROM forms f
            INNER JOIN form_submissions fs ON f.id = fs.form_id
            WHERE f.form_name IS NOT NULL AND f.form_name != ''
            ORDER BY f.form_name";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $forms = $stmt->fetchAll(PDO::FETCH_ASSOC);

    echo json_encode([
        'success' => true,
        'submissions' => $submissions,
        'pages' => $pages,
        'forms' => $forms,
        'total' => count($submissions)
    ]);

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
