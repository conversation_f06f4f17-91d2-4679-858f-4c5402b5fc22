<?php
/**
 * Database Diagnostic Tool
 * Shows exactly what's in the database and what's missing
 */

require_once 'config/database.php';

echo "<h1>Database Diagnostic Tool</h1>";

try {
    $database = new Database();
    $db = $database->getConnection();
    
    if (!$db) {
        throw new Exception("Database connection failed");
    }
    
    echo "<p>✅ Database connection successful</p>";
    
    // Show all tables
    echo "<h2>All Tables in Database</h2>";
    $sql = "SHOW TABLES";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    if (empty($tables)) {
        echo "<p>❌ No tables found in database!</p>";
    } else {
        echo "<ul>";
        foreach ($tables as $table) {
            echo "<li>$table</li>";
        }
        echo "</ul>";
    }
    
    // Check page_shares table specifically
    echo "<h2>page_shares Table Analysis</h2>";
    
    if (in_array('page_shares', $tables)) {
        echo "<p>✅ page_shares table exists</p>";
        
        // Show structure
        $sql = "DESCRIBE page_shares";
        $stmt = $db->prepare($sql);
        $stmt->execute();
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<h3>Current Structure:</h3>";
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
        
        $hasPageId = false;
        foreach ($columns as $col) {
            $highlight = ($col['Field'] === 'page_id') ? 'style="background-color: #d4edda;"' : '';
            echo "<tr $highlight>";
            echo "<td>{$col['Field']}</td>";
            echo "<td>{$col['Type']}</td>";
            echo "<td>{$col['Null']}</td>";
            echo "<td>{$col['Key']}</td>";
            echo "<td>{$col['Default']}</td>";
            echo "</tr>";
            
            if ($col['Field'] === 'page_id') {
                $hasPageId = true;
            }
        }
        echo "</table>";
        
        if ($hasPageId) {
            echo "<p>✅ page_id column exists</p>";
        } else {
            echo "<p>❌ page_id column is MISSING!</p>";
            echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
            echo "<h4>This is the problem!</h4>";
            echo "<p>The page_shares table exists but doesn't have the page_id column.</p>";
            echo "</div>";
        }
        
        // Show sample data
        $sql = "SELECT COUNT(*) as count FROM page_shares";
        $stmt = $db->prepare($sql);
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        echo "<p><strong>Records in table:</strong> {$result['count']}</p>";
        
    } else {
        echo "<p>❌ page_shares table does NOT exist</p>";
    }
    
    // Check pages table
    echo "<h2>pages Table Analysis</h2>";
    
    if (in_array('pages', $tables)) {
        echo "<p>✅ pages table exists</p>";
        
        $sql = "SELECT COUNT(*) as count FROM pages";
        $stmt = $db->prepare($sql);
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        echo "<p><strong>Pages available:</strong> {$result['count']}</p>";
        
        if ($result['count'] > 0) {
            $sql = "SELECT id, title, original_filename FROM pages LIMIT 3";
            $stmt = $db->prepare($sql);
            $stmt->execute();
            $pages = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo "<h4>Sample Pages:</h4>";
            echo "<ul>";
            foreach ($pages as $page) {
                echo "<li>ID: {$page['id']} - {$page['title']} ({$page['original_filename']})</li>";
            }
            echo "</ul>";
        }
        
    } else {
        echo "<p>❌ pages table does NOT exist</p>";
    }
    
    // Test the problematic query
    echo "<h2>Query Test</h2>";
    
    try {
        $sql = "SELECT ps.*, p.id as page_id, p.title as page_title 
                FROM page_shares ps 
                JOIN pages p ON ps.page_id = p.id 
                WHERE ps.share_token = ? AND ps.is_active = 1";
        $stmt = $db->prepare($sql);
        $stmt->execute(['test_token']);
        
        echo "<p>✅ Query executed successfully (no syntax errors)</p>";
        
    } catch (Exception $e) {
        echo "<p>❌ Query failed: " . htmlspecialchars($e->getMessage()) . "</p>";
        
        if (strpos($e->getMessage(), 'page_id') !== false) {
            echo "<div style='background: #fff3cd; color: #856404; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
            echo "<h4>Confirmed: page_id column is missing</h4>";
            echo "<p>The error confirms that the page_shares table doesn't have a page_id column.</p>";
            echo "</div>";
        }
    }
    
    // Show foreign key constraints
    echo "<h2>Foreign Key Constraints</h2>";
    
    $sql = "SELECT 
                TABLE_NAME,
                COLUMN_NAME,
                CONSTRAINT_NAME,
                REFERENCED_TABLE_NAME,
                REFERENCED_COLUMN_NAME
            FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
            WHERE REFERENCED_TABLE_SCHEMA = DATABASE()
            AND REFERENCED_TABLE_NAME IS NOT NULL";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $constraints = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($constraints)) {
        echo "<p>No foreign key constraints found</p>";
    } else {
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>Table</th><th>Column</th><th>Constraint</th><th>References Table</th><th>References Column</th></tr>";
        foreach ($constraints as $constraint) {
            echo "<tr>";
            echo "<td>{$constraint['TABLE_NAME']}</td>";
            echo "<td>{$constraint['COLUMN_NAME']}</td>";
            echo "<td>{$constraint['CONSTRAINT_NAME']}</td>";
            echo "<td>{$constraint['REFERENCED_TABLE_NAME']}</td>";
            echo "<td>{$constraint['REFERENCED_COLUMN_NAME']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
} catch (Exception $e) {
    echo "<p>❌ Error: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "<h2>Recommended Actions</h2>";
echo "<div style='background: #e7f3ff; color: #004085; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h4>To Fix This Issue:</h4>";
echo "<ol>";
echo "<li><strong>Use phpMyAdmin:</strong> Go to phpMyAdmin and run the SQL script in <code>direct_sql_fix.sql</code></li>";
echo "<li><strong>Or use command line:</strong> Run <code>mysql -u root -p your_database < direct_sql_fix.sql</code></li>";
echo "<li><strong>Manual approach:</strong> In phpMyAdmin, go to the page_shares table and add a column named 'page_id' of type INT NOT NULL</li>";
echo "</ol>";
echo "</div>";

echo "<h2>Quick Links</h2>";
echo "<ul>";
echo "<li><a href='direct_sql_fix.sql' target='_blank'>View SQL Fix Script</a></li>";
echo "<li><a href='fix_foreign_key_issue.php'>Try Automated Fix</a></li>";
echo "<li><a href='index.html'>Main Application</a></li>";
echo "</ul>";

?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
table { border-collapse: collapse; margin: 10px 0; }
th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
th { background-color: #f2f2f2; }
h1, h2, h3, h4 { color: #333; }
code { background: #f5f5f5; padding: 2px 4px; border-radius: 3px; }
</style>
