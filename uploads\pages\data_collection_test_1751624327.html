<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Data Collection Test Page</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        .form-section { background: #f5f5f5; padding: 20px; margin: 20px 0; border-radius: 8px; }
        .form-group { margin: 15px 0; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, textarea, select { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; box-sizing: border-box; }
        button { background: #007cba; color: white; padding: 12px 24px; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #005a8b; }
        .standalone-input { margin: 20px 0; padding: 15px; background: #e7f3ff; border-radius: 8px; }
    </style>
</head>
<body>
    <h1>Data Collection Test Page</h1>
    
    <div class="form-section">
        <h2>Contact Form</h2>
        <form name="contact_form" id="contact_form">
            <div class="form-group">
                <label for="name">Name:</label>
                <input type="text" id="name" name="name" required>
            </div>
            <div class="form-group">
                <label for="email">Email:</label>
                <input type="email" id="email" name="email" required>
            </div>
            <div class="form-group">
                <label for="message">Message:</label>
                <textarea id="message" name="message" rows="4" required></textarea>
            </div>
            <button type="submit">Send Message</button>
        </form>
    </div>
    
    <div class="form-section">
        <h2>Survey Form</h2>
        <form name="survey_form" id="survey_form">
            <div class="form-group">
                <label for="rating">How would you rate our service?</label>
                <select id="rating" name="rating">
                    <option value="">Select rating</option>
                    <option value="excellent">Excellent</option>
                    <option value="good">Good</option>
                    <option value="average">Average</option>
                    <option value="poor">Poor</option>
                </select>
            </div>
            <div class="form-group">
                <label>Which features do you use?</label>
                <label><input type="checkbox" name="features[]" value="upload"> File Upload</label>
                <label><input type="checkbox" name="features[]" value="sharing"> Page Sharing</label>
                <label><input type="checkbox" name="features[]" value="forms"> Form Collection</label>
            </div>
            <div class="form-group">
                <label>How did you hear about us?</label>
                <label><input type="radio" name="source" value="search"> Search Engine</label>
                <label><input type="radio" name="source" value="social"> Social Media</label>
                <label><input type="radio" name="source" value="friend"> Friend Referral</label>
            </div>
            <button type="submit">Submit Survey</button>
        </form>
    </div>
    
    <div class="standalone-input">
        <h2>Standalone Input (Not in Form)</h2>
        <label for="newsletter">Subscribe to Newsletter:</label>
        <input type="email" id="newsletter" name="newsletter_email" placeholder="Enter your email">
        <button type="button" onclick="alert('This should be captured too!')">Subscribe</button>
    </div>
    
    <div class="form-section">
        <h2>File Upload Form</h2>
        <form name="upload_form" enctype="multipart/form-data">
            <div class="form-group">
                <label for="file">Choose File:</label>
                <input type="file" id="file" name="uploaded_file">
            </div>
            <div class="form-group">
                <label for="description">File Description:</label>
                <input type="text" id="description" name="file_description">
            </div>
            <button type="submit">Upload File</button>
        </form>
    </div>
</body>
</html>