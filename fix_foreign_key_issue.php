<?php
/**
 * Fix Foreign Key Constraint Issue
 * Properly handles foreign key constraints when fixing the database
 */

require_once 'config/database.php';

echo "<h1>Fix Foreign Key Constraint Issue</h1>";

try {
    $database = new Database();
    $db = $database->getConnection();
    
    if (!$db) {
        throw new Exception("Database connection failed");
    }
    
    echo "<p>✅ Database connected</p>";
    
    // Disable foreign key checks temporarily
    echo "<p>🔧 Disabling foreign key checks...</p>";
    $db->exec("SET FOREIGN_KEY_CHECKS = 0");
    
    // Check current page_shares structure
    echo "<h2>Current page_shares Structure</h2>";
    
    try {
        $sql = "DESCRIBE page_shares";
        $stmt = $db->prepare($sql);
        $stmt->execute();
        $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        echo "<p><strong>Current columns:</strong> " . implode(', ', $columns) . "</p>";
        
        if (!in_array('page_id', $columns)) {
            echo "<p>❌ page_id column is missing!</p>";
            
            // Add the missing column instead of dropping the table
            echo "<p>🔧 Adding page_id column...</p>";
            
            $sql = "ALTER TABLE page_shares ADD COLUMN page_id INT NOT NULL AFTER id";
            $db->exec($sql);
            echo "<p>✅ page_id column added</p>";
            
            // Add index
            $sql = "ALTER TABLE page_shares ADD INDEX idx_page_id (page_id)";
            $db->exec($sql);
            echo "<p>✅ Index added for page_id</p>";
            
        } else {
            echo "<p>✅ page_id column already exists</p>";
        }
        
    } catch (Exception $e) {
        echo "<p>❌ Error checking table: " . $e->getMessage() . "</p>";
        echo "<p>🔧 Table might not exist, creating it...</p>";
        
        // Drop tables in correct order (child tables first)
        $db->exec("DROP TABLE IF EXISTS share_access_log");
        $db->exec("DROP TABLE IF EXISTS form_submissions");
        $db->exec("DROP TABLE IF EXISTS page_shares");
        
        // Create page_shares table
        $sql = "CREATE TABLE page_shares (
            id INT AUTO_INCREMENT PRIMARY KEY,
            page_id INT NOT NULL,
            share_token VARCHAR(64) NOT NULL UNIQUE,
            short_code VARCHAR(10) NOT NULL UNIQUE,
            title VARCHAR(255),
            description TEXT,
            password_hash VARCHAR(255),
            expires_at TIMESTAMP NULL,
            max_views INT NULL,
            view_count INT DEFAULT 0,
            allow_download BOOLEAN DEFAULT FALSE,
            show_forms BOOLEAN DEFAULT TRUE,
            show_metadata BOOLEAN DEFAULT FALSE,
            is_active BOOLEAN DEFAULT TRUE,
            created_by INT DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_page_id (page_id),
            INDEX idx_share_token (share_token),
            INDEX idx_short_code (short_code),
            INDEX idx_created_at (created_at),
            INDEX idx_is_active (is_active)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        $db->exec($sql);
        echo "<p>✅ page_shares table created</p>";
    }
    
    // Ensure form_submissions table exists
    echo "<h2>Ensuring form_submissions Table</h2>";
    
    $sql = "CREATE TABLE IF NOT EXISTS form_submissions (
        id INT AUTO_INCREMENT PRIMARY KEY,
        page_id INT NOT NULL,
        form_id INT NULL,
        share_id INT NULL,
        form_data JSON NOT NULL,
        ip_address VARCHAR(45),
        user_agent TEXT,
        referrer TEXT,
        submitted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        processed BOOLEAN DEFAULT FALSE,
        notes TEXT,
        INDEX idx_page_id (page_id),
        INDEX idx_form_id (form_id),
        INDEX idx_share_id (share_id),
        INDEX idx_submitted_at (submitted_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $db->exec($sql);
    echo "<p>✅ form_submissions table ready</p>";
    
    // Ensure share_access_log table exists (without foreign key for now)
    echo "<h2>Ensuring share_access_log Table</h2>";
    
    $sql = "CREATE TABLE IF NOT EXISTS share_access_log (
        id INT AUTO_INCREMENT PRIMARY KEY,
        share_id INT NOT NULL,
        ip_address VARCHAR(45),
        user_agent TEXT,
        referrer TEXT,
        access_type ENUM('view', 'form_submission', 'download') DEFAULT 'view',
        accessed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_share_id (share_id),
        INDEX idx_accessed_at (accessed_at),
        INDEX idx_access_type (access_type)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $db->exec($sql);
    echo "<p>✅ share_access_log table ready</p>";
    
    // Re-enable foreign key checks
    echo "<p>🔧 Re-enabling foreign key checks...</p>";
    $db->exec("SET FOREIGN_KEY_CHECKS = 1");
    
    // Test the problematic query
    echo "<h2>Testing the Fix</h2>";
    
    $sql = "SELECT ps.*, p.id as page_id, p.title as page_title 
            FROM page_shares ps 
            JOIN pages p ON ps.page_id = p.id 
            WHERE ps.share_token = ? AND ps.is_active = 1";
    $stmt = $db->prepare($sql);
    $stmt->execute(['test_token']);
    
    echo "<p>✅ Query test passed - no more column errors!</p>";
    
    // Create a test share if we have pages
    echo "<h2>Creating Test Share</h2>";
    
    $sql = "SELECT id, title, original_filename FROM pages LIMIT 1";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $page = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($page) {
        echo "<p>Found page: {$page['title']} (ID: {$page['id']})</p>";
        
        // Check if a test share already exists
        $sql = "SELECT COUNT(*) as count FROM page_shares WHERE page_id = ?";
        $stmt = $db->prepare($sql);
        $stmt->execute([$page['id']]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($result['count'] == 0) {
            $shareToken = bin2hex(random_bytes(16));
            $shortCode = substr(str_shuffle('ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'), 0, 6);
            
            $sql = "INSERT INTO page_shares (page_id, share_token, short_code, title, show_forms, is_active) 
                    VALUES (?, ?, ?, ?, 1, 1)";
            $stmt = $db->prepare($sql);
            $stmt->execute([$page['id'], $shareToken, $shortCode, 'Test Share - FK Fixed']);
            
            $shareId = $db->lastInsertId();
            echo "<p>✅ Test share created with ID: $shareId</p>";
            echo "<p><strong>Share Token:</strong> $shareToken</p>";
            
            // Generate URL
            $baseUrl = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http') . 
                      '://' . $_SERVER['HTTP_HOST'] . 
                      rtrim(dirname($_SERVER['SCRIPT_NAME']), '/');
            $shareUrl = $baseUrl . '/view.php?token=' . $shareToken;
            
            echo "<p><strong>Share URL:</strong> <a href='$shareUrl' target='_blank'>$shareUrl</a></p>";
        } else {
            echo "<p>✅ Test share already exists for this page</p>";
        }
        
    } else {
        echo "<p>⚠️ No pages found to create test share</p>";
    }
    
    // Show final table structure
    echo "<h2>Final Table Structure</h2>";
    
    $sql = "DESCRIBE page_shares";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    foreach ($columns as $col) {
        $highlight = ($col['Field'] === 'page_id') ? 'style="background-color: #d4edda;"' : '';
        echo "<tr $highlight>";
        echo "<td>{$col['Field']}</td>";
        echo "<td>{$col['Type']}</td>";
        echo "<td>{$col['Null']}</td>";
        echo "<td>{$col['Key']}</td>";
        echo "<td>{$col['Default']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<div style='background: #d4edda; color: #155724; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h2>🎉 Foreign Key Issue Fixed!</h2>";
    echo "<p>The database has been fixed properly. The 'page_id' column error should now be resolved.</p>";
    echo "<p>Foreign key constraints have been handled correctly.</p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h2>❌ Fix Failed</h2>";
    echo "<p>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
    
    // Try to re-enable foreign key checks even if there was an error
    try {
        $db->exec("SET FOREIGN_KEY_CHECKS = 1");
    } catch (Exception $e2) {
        // Ignore this error
    }
}

echo "<h2>Next Steps</h2>";
echo "<p>Now test the system:</p>";
echo "<ul>";
echo "<li><a href='test_form_submission.php'>Test Form Submission System</a></li>";
echo "<li><a href='test_submit_form.php'>Test Submit Form Handler</a></li>";
echo "<li><a href='index.html'>Main Application</a></li>";
echo "</ul>";

?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
table { border-collapse: collapse; margin: 10px 0; }
th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
th { background-color: #f2f2f2; }
h1, h2 { color: #333; }
</style>
