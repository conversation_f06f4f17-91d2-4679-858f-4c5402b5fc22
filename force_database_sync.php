<?php
/**
 * Force Database Synchronization
 * Ensures the actual database matches the schema in config/database.php
 */

require_once 'config/database.php';

echo "<h1>Force Database Synchronization</h1>";

try {
    $database = new Database();
    $db = $database->getConnection();
    
    if (!$db) {
        throw new Exception("Database connection failed");
    }
    
    echo "<p>✅ Database connection successful</p>";
    
    // Force execution of createTables method
    echo "<h2>Executing createTables() Method</h2>";
    
    $result = $database->createTables();
    
    if ($result) {
        echo "<p>✅ createTables() executed successfully</p>";
    } else {
        echo "<p>⚠️ createTables() returned false, but continuing...</p>";
    }
    
    // Check current page_shares table structure
    echo "<h2>Current page_shares Table Structure</h2>";
    
    try {
        $sql = "DESCRIBE page_shares";
        $stmt = $db->prepare($sql);
        $stmt->execute();
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
        
        $hasPageId = false;
        foreach ($columns as $col) {
            echo "<tr>";
            echo "<td>{$col['Field']}</td>";
            echo "<td>{$col['Type']}</td>";
            echo "<td>{$col['Null']}</td>";
            echo "<td>{$col['Key']}</td>";
            echo "<td>{$col['Default']}</td>";
            echo "</tr>";
            
            if ($col['Field'] === 'page_id') {
                $hasPageId = true;
            }
        }
        echo "</table>";
        
        if (!$hasPageId) {
            echo "<p>❌ page_id column is still missing!</p>";
            echo "<h3>Manually Adding page_id Column</h3>";
            
            // Drop and recreate the table with correct structure
            echo "<p>Dropping and recreating page_shares table...</p>";
            
            // Backup any existing data
            $sql = "SELECT * FROM page_shares";
            $stmt = $db->prepare($sql);
            $stmt->execute();
            $backupData = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // Drop the table
            $sql = "DROP TABLE IF EXISTS page_shares";
            $db->exec($sql);
            echo "<p>✅ Old table dropped</p>";
            
            // Recreate with correct structure (from your config file)
            $sql = "CREATE TABLE page_shares (
                id INT AUTO_INCREMENT PRIMARY KEY,
                page_id INT NOT NULL,
                share_token VARCHAR(64) NOT NULL UNIQUE,
                short_code VARCHAR(10) NOT NULL UNIQUE,
                title VARCHAR(255),
                description TEXT,
                password_hash VARCHAR(255),
                expires_at TIMESTAMP NULL,
                max_views INT NULL,
                view_count INT DEFAULT 0,
                allow_download BOOLEAN DEFAULT FALSE,
                show_forms BOOLEAN DEFAULT TRUE,
                show_metadata BOOLEAN DEFAULT FALSE,
                is_active BOOLEAN DEFAULT TRUE,
                created_by INT DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_page_id (page_id),
                INDEX idx_share_token (share_token),
                INDEX idx_short_code (short_code),
                INDEX idx_created_at (created_at),
                INDEX idx_is_active (is_active)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
            
            $db->exec($sql);
            echo "<p>✅ Table recreated with correct structure</p>";
            
            // Restore data if it had page_id
            if (!empty($backupData)) {
                echo "<p>Attempting to restore " . count($backupData) . " records...</p>";
                foreach ($backupData as $row) {
                    if (isset($row['page_id'])) {
                        $sql = "INSERT INTO page_shares (page_id, share_token, short_code, title, description, password_hash, expires_at, max_views, view_count, allow_download, show_forms, show_metadata, is_active, created_by, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
                        $stmt = $db->prepare($sql);
                        $stmt->execute([
                            $row['page_id'],
                            $row['share_token'],
                            $row['short_code'],
                            $row['title'],
                            $row['description'],
                            $row['password_hash'],
                            $row['expires_at'],
                            $row['max_views'],
                            $row['view_count'],
                            $row['allow_download'] ?? 0,
                            $row['show_forms'] ?? 1,
                            $row['show_metadata'] ?? 0,
                            $row['is_active'] ?? 1,
                            $row['created_by'] ?? 1,
                            $row['created_at'],
                            $row['updated_at']
                        ]);
                    }
                }
                echo "<p>✅ Data restored</p>";
            }
            
        } else {
            echo "<p>✅ page_id column exists</p>";
        }
        
    } catch (Exception $e) {
        echo "<p>❌ Error checking table structure: " . $e->getMessage() . "</p>";
        
        // Table might not exist, create it
        echo "<p>Creating page_shares table from scratch...</p>";
        
        $sql = "CREATE TABLE page_shares (
            id INT AUTO_INCREMENT PRIMARY KEY,
            page_id INT NOT NULL,
            share_token VARCHAR(64) NOT NULL UNIQUE,
            short_code VARCHAR(10) NOT NULL UNIQUE,
            title VARCHAR(255),
            description TEXT,
            password_hash VARCHAR(255),
            expires_at TIMESTAMP NULL,
            max_views INT NULL,
            view_count INT DEFAULT 0,
            allow_download BOOLEAN DEFAULT FALSE,
            show_forms BOOLEAN DEFAULT TRUE,
            show_metadata BOOLEAN DEFAULT FALSE,
            is_active BOOLEAN DEFAULT TRUE,
            created_by INT DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_page_id (page_id),
            INDEX idx_share_token (share_token),
            INDEX idx_short_code (short_code),
            INDEX idx_created_at (created_at),
            INDEX idx_is_active (is_active)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        $db->exec($sql);
        echo "<p>✅ page_shares table created</p>";
    }
    
    // Ensure form_submissions table exists
    echo "<h2>Ensuring form_submissions Table</h2>";
    
    $sql = "CREATE TABLE IF NOT EXISTS form_submissions (
        id INT AUTO_INCREMENT PRIMARY KEY,
        page_id INT NOT NULL,
        form_id INT NULL,
        share_id INT NULL,
        form_data JSON NOT NULL,
        ip_address VARCHAR(45),
        user_agent TEXT,
        referrer TEXT,
        submitted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        processed BOOLEAN DEFAULT FALSE,
        notes TEXT,
        INDEX idx_page_id (page_id),
        INDEX idx_form_id (form_id),
        INDEX idx_share_id (share_id),
        INDEX idx_submitted_at (submitted_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $db->exec($sql);
    echo "<p>✅ form_submissions table ensured</p>";
    
    // Test the problematic query
    echo "<h2>Testing the Fixed Query</h2>";
    
    $sql = "SELECT ps.*, p.id as page_id, p.title as page_title 
            FROM page_shares ps 
            JOIN pages p ON ps.page_id = p.id 
            WHERE ps.share_token = ? AND ps.is_active = 1";
    $stmt = $db->prepare($sql);
    $stmt->execute(['test_token']);
    
    echo "<p>✅ Query executed without errors!</p>";
    
    // Create a test share if we have pages
    echo "<h2>Creating Test Share</h2>";
    
    $sql = "SELECT id, title, original_filename FROM pages LIMIT 1";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $page = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($page) {
        $shareToken = bin2hex(random_bytes(16));
        $shortCode = substr(str_shuffle('ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'), 0, 6);
        
        $sql = "INSERT INTO page_shares (page_id, share_token, short_code, title, show_forms, is_active) 
                VALUES (?, ?, ?, ?, 1, 1)";
        $stmt = $db->prepare($sql);
        $stmt->execute([$page['id'], $shareToken, $shortCode, 'Sync Test Share']);
        
        $shareId = $db->lastInsertId();
        echo "<p>✅ Test share created with ID: $shareId</p>";
        echo "<p><strong>Share Token:</strong> $shareToken</p>";
        
        // Generate URL
        $baseUrl = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http') . 
                  '://' . $_SERVER['HTTP_HOST'] . 
                  rtrim(dirname($_SERVER['SCRIPT_NAME']), '/');
        $shareUrl = $baseUrl . '/view.php?token=' . $shareToken;
        
        echo "<p><strong>Share URL:</strong> <a href='$shareUrl' target='_blank'>$shareUrl</a></p>";
        
    } else {
        echo "<p>⚠️ No pages found to create test share</p>";
    }
    
    echo "<h2>✅ Database Synchronization Complete</h2>";
    echo "<p>The database structure now matches the schema in config/database.php</p>";
    
} catch (Exception $e) {
    echo "<p>❌ Error: " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "<h2>Test the Fix</h2>";
echo "<p>Now try these tests:</p>";
echo "<ul>";
echo "<li><a href='test_form_submission.php'>Test Form Submission System</a></li>";
echo "<li><a href='test_submit_form.php'>Test Submit Form Handler</a></li>";
echo "<li><a href='index.html'>Main Application</a></li>";
echo "</ul>";

?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
table { border-collapse: collapse; margin: 10px 0; }
th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
th { background-color: #f2f2f2; }
h1, h2, h3 { color: #333; }
</style>
