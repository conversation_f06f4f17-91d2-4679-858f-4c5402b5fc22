<?php
/**
 * Create Database Tables
 * Runs the database creation methods from the Database class
 */

require_once 'config/database.php';

echo "<h1>Database Table Creation</h1>";

try {
    $database = new Database();
    $db = $database->getConnection();
    
    if (!$db) {
        throw new Exception("Database connection failed");
    }
    
    echo "<p>✅ Database connection successful</p>";
    
    // Run the createTables method
    echo "<h2>Creating Database Tables</h2>";
    
    $result = $database->createTables();
    
    if ($result) {
        echo "<p>✅ Database tables created successfully</p>";
    } else {
        echo "<p>❌ Failed to create database tables</p>";
    }
    
    // Check what tables were created
    echo "<h2>Verifying Tables</h2>";
    
    $sql = "SHOW TABLES";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo "<p><strong>Created tables:</strong></p>";
    echo "<ul>";
    foreach ($tables as $table) {
        echo "<li>$table</li>";
    }
    echo "</ul>";
    
    // Check page_shares table specifically
    if (in_array('page_shares', $tables)) {
        echo "<h3>page_shares Table Structure</h3>";
        $sql = "DESCRIBE page_shares";
        $stmt = $db->prepare($sql);
        $stmt->execute();
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
        foreach ($columns as $col) {
            echo "<tr>";
            echo "<td>{$col['Field']}</td>";
            echo "<td>{$col['Type']}</td>";
            echo "<td>{$col['Null']}</td>";
            echo "<td>{$col['Key']}</td>";
            echo "<td>{$col['Default']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // Test the sharing system
    echo "<h2>Testing Sharing System</h2>";
    
    // Check if we have any pages
    $sql = "SELECT COUNT(*) as count FROM pages";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    $pageCount = $result['count'];
    
    echo "<p>Pages in database: $pageCount</p>";
    
    if ($pageCount == 0) {
        echo "<p>Creating test page...</p>";
        
        // Create uploads directory if it doesn't exist
        if (!is_dir('uploads/pages')) {
            mkdir('uploads/pages', 0755, true);
        }
        
        // Create test HTML file
        $testHtml = '<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Contact Form</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 600px; margin: 50px auto; padding: 20px; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, textarea { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        button { background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
    </style>
</head>
<body>
    <h1>Contact Us</h1>
    <form method="POST" action="">
        <div class="form-group">
            <label for="name">Name:</label>
            <input type="text" id="name" name="name" required>
        </div>
        <div class="form-group">
            <label for="email">Email:</label>
            <input type="email" id="email" name="email" required>
        </div>
        <div class="form-group">
            <label for="message">Message:</label>
            <textarea id="message" name="message" rows="5" required></textarea>
        </div>
        <button type="submit">Send Message</button>
    </form>
</body>
</html>';
        
        $testFile = 'uploads/pages/test_contact_' . time() . '.html';
        file_put_contents($testFile, $testHtml);
        
        // Insert into database
        $sql = "INSERT INTO pages (filename, original_filename, title, file_path, file_size, file_hash) 
                VALUES (?, ?, ?, ?, ?, ?)";
        $stmt = $db->prepare($sql);
        $stmt->execute([
            basename($testFile),
            'test_contact.html',
            'Test Contact Form',
            $testFile,
            strlen($testHtml),
            hash('sha256', $testHtml)
        ]);
        
        $pageId = $db->lastInsertId();
        echo "<p>✅ Test page created with ID: $pageId</p>";
    }
    
    // Try to create a test share
    $sql = "SELECT id, title, original_filename FROM pages LIMIT 1";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $page = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($page) {
        echo "<p>Testing with page: {$page['title']} (ID: {$page['id']})</p>";
        
        try {
            $shareToken = bin2hex(random_bytes(16));
            $shortCode = substr(str_shuffle('ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'), 0, 6);
            
            $sql = "INSERT INTO page_shares (page_id, share_token, short_code, title, show_forms, is_active) 
                    VALUES (?, ?, ?, ?, 1, 1)";
            $stmt = $db->prepare($sql);
            $stmt->execute([$page['id'], $shareToken, $shortCode, 'Database Test Share']);
            
            $shareId = $db->lastInsertId();
            echo "<p>✅ Test share created successfully!</p>";
            echo "<p><strong>Share ID:</strong> $shareId</p>";
            echo "<p><strong>Share Token:</strong> $shareToken</p>";
            echo "<p><strong>Short Code:</strong> $shortCode</p>";
            
            // Generate share URL
            $baseUrl = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http') . 
                      '://' . $_SERVER['HTTP_HOST'] . 
                      rtrim(dirname($_SERVER['SCRIPT_NAME']), '/');
            $shareUrl = $baseUrl . '/view.php?token=' . $shareToken;
            
            echo "<p><strong>Share URL:</strong> <a href='$shareUrl' target='_blank'>$shareUrl</a></p>";
            
            // Test the problematic query
            echo "<h3>Testing Query</h3>";
            $sql = "SELECT ps.*, p.id as page_id, p.title as page_title 
                    FROM page_shares ps 
                    JOIN pages p ON ps.page_id = p.id 
                    WHERE ps.share_token = ? AND ps.is_active = 1";
            $stmt = $db->prepare($sql);
            $stmt->execute([$shareToken]);
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($result) {
                echo "<p>✅ Query executed successfully!</p>";
                echo "<p>Found share: {$result['title']} for page: {$result['page_title']}</p>";
            } else {
                echo "<p>❌ Query returned no results</p>";
            }
            
        } catch (Exception $e) {
            echo "<p>❌ Error creating test share: " . $e->getMessage() . "</p>";
        }
    }
    
} catch (Exception $e) {
    echo "<p>❌ Error: " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "<h2>Next Steps</h2>";
echo "<p>If tables were created successfully, try:</p>";
echo "<ul>";
echo "<li><a href='test_form_submission.php'>Test Form Submission System</a></li>";
echo "<li><a href='test_view_fix.php'>Test View System</a></li>";
echo "<li><a href='index.html'>Main Application</a></li>";
echo "</ul>";

?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
table { border-collapse: collapse; margin: 10px 0; }
th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
th { background-color: #f2f2f2; }
h1, h2, h3 { color: #333; }
</style>
