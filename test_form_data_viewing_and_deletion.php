<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Form Data Viewing and Deletion</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 20px; line-height: 1.6; }
        .container { max-width: 1200px; margin: 0 auto; }
        .card { background: white; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); margin: 20px 0; padding: 20px; }
        .btn { background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; text-decoration: none; display: inline-block; margin: 5px; }
        .btn:hover { background: #005a8b; }
        .btn-success { background: #28a745; }
        .btn-danger { background: #dc3545; }
        .btn-secondary { background: #6c757d; }
        .result { margin: 10px 0; padding: 15px; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .warning { background: #fff3cd; color: #856404; }
        .info { background: #d1ecf1; color: #0c5460; }
        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        th, td { border: 1px solid #ddd; padding: 12px; text-align: left; }
        th { background: #f8f9fa; font-weight: 600; }
        .form-group { margin: 15px 0; }
        .form-control { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px; box-sizing: border-box; }
        .json-data { background: #f8f9fa; padding: 10px; border-radius: 5px; font-family: monospace; white-space: pre-wrap; max-height: 200px; overflow-y: auto; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Test Form Data Viewing and Deletion</h1>
        
        <div class="result success">
            <h3>✅ Features Implemented</h3>
            <p>The form data viewing and deletion functionality has been enhanced with:</p>
            <ul>
                <li><strong>Dynamic Column Detection:</strong> Automatically detects 'form_data' vs 'submission_data' columns</li>
                <li><strong>Enhanced Data Parsing:</strong> Robust JSON parsing with proper error handling</li>
                <li><strong>Delete Functionality:</strong> Ability to delete individual form submissions</li>
                <li><strong>Improved UI:</strong> Better modal display with action buttons</li>
                <li><strong>API Integration:</strong> Uses sharing_manager.php for all operations</li>
            </ul>
        </div>

        <div class="card">
            <h2>1. Submit Test Data</h2>
            <p>First, submit some test data to have submissions to view and delete:</p>
            
            <form id="testForm" style="background: #f8f9fa; padding: 20px; border-radius: 8px;">
                <input type="hidden" name="_page_id" value="1">
                <input type="hidden" name="_form_name" value="test_viewing_deletion">
                
                <div class="form-group">
                    <label for="name">Name:</label>
                    <input type="text" id="name" name="name" class="form-control" value="Test User">
                </div>
                
                <div class="form-group">
                    <label for="email">Email:</label>
                    <input type="email" id="email" name="email" class="form-control" value="<EMAIL>">
                </div>
                
                <div class="form-group">
                    <label for="message">Message:</label>
                    <textarea id="message" name="message" class="form-control" rows="3">This is a test submission for viewing and deletion functionality.</textarea>
                </div>
                
                <div class="form-group">
                    <label for="category">Category:</label>
                    <select id="category" name="category" class="form-control">
                        <option value="general">General Inquiry</option>
                        <option value="support">Support Request</option>
                        <option value="feedback">Feedback</option>
                        <option value="bug">Bug Report</option>
                    </select>
                </div>
                
                <button type="submit" class="btn">Submit Test Data</button>
            </form>
            
            <div id="submitResult"></div>
        </div>

        <div class="card">
            <h2>2. View Form Submissions</h2>
            <p>Test the enhanced form data viewing functionality:</p>
            
            <button onclick="loadSubmissions()" class="btn btn-success">Load All Submissions</button>
            <button onclick="loadSubmissions(1)" class="btn btn-secondary">Load Page 1 Submissions</button>
            
            <div id="submissionsResult"></div>
        </div>

        <div class="card">
            <h2>3. Test Individual Operations</h2>
            <p>Test specific submission operations:</p>
            
            <div class="form-group">
                <label for="submissionId">Submission ID:</label>
                <input type="number" id="submissionId" class="form-control" placeholder="Enter submission ID">
            </div>
            
            <button onclick="viewSubmission()" class="btn">View Submission</button>
            <button onclick="deleteSubmission()" class="btn btn-danger">Delete Submission</button>
            
            <div id="operationResult"></div>
        </div>

        <div class="card">
            <h2>4. Database Integration Test</h2>
            <p>Test the database integration and column detection:</p>
            
            <button onclick="testDatabaseIntegration()" class="btn btn-secondary">Test Database Integration</button>
            
            <div id="dbTestResult"></div>
        </div>

        <div class="card">
            <h2>5. Expected Results</h2>
            <div class="info">
                <h3>✅ What Should Work Now</h3>
                <table>
                    <tr>
                        <th>Feature</th>
                        <th>Expected Behavior</th>
                        <th>Previous Issue</th>
                    </tr>
                    <tr>
                        <td><strong>View Submissions</strong></td>
                        <td>✅ Shows actual form data with proper formatting</td>
                        <td>❌ Displayed "Invalid data"</td>
                    </tr>
                    <tr>
                        <td><strong>Submission Details</strong></td>
                        <td>✅ Modal shows formatted JSON data</td>
                        <td>❌ Modal showed parsing errors</td>
                    </tr>
                    <tr>
                        <td><strong>Delete Submissions</strong></td>
                        <td>✅ Delete button removes submissions</td>
                        <td>❌ No delete functionality</td>
                    </tr>
                    <tr>
                        <td><strong>Column Detection</strong></td>
                        <td>✅ Works with both 'form_data' and 'submission_data'</td>
                        <td>❌ Hard-coded column names</td>
                    </tr>
                    <tr>
                        <td><strong>Error Handling</strong></td>
                        <td>✅ Graceful handling of malformed data</td>
                        <td>❌ Generic "Invalid data" errors</td>
                    </tr>
                </table>
            </div>
        </div>

        <div class="card">
            <h2>Quick Action Links</h2>
            <a href="index.html#database" target="_blank" class="btn">Open Database Tab</a>
            <a href="enhanced_data_collection.php" target="_blank" class="btn btn-success">Data Collection Manager</a>
            <a href="test_submission_data_viewing.php" target="_blank" class="btn btn-secondary">Database Diagnostic</a>
        </div>
    </div>

    <script>
        // Submit test form
        document.getElementById('testForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const resultDiv = document.getElementById('submitResult');
            
            resultDiv.innerHTML = '<div class="result info">Submitting test data...</div>';
            
            fetch('enhanced_submit_form.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    resultDiv.innerHTML = `
                        <div class="result success">
                            <h4>✅ Test Data Submitted Successfully</h4>
                            <p><strong>Submission ID:</strong> ${data.submission_id}</p>
                            <p>You can now test viewing and deletion with this submission.</p>
                        </div>
                    `;
                    
                    // Auto-fill the submission ID field
                    document.getElementById('submissionId').value = data.submission_id;
                } else {
                    resultDiv.innerHTML = `
                        <div class="result error">
                            <h4>❌ Submission Failed</h4>
                            <p>${data.message}</p>
                        </div>
                    `;
                }
            })
            .catch(error => {
                console.error('Submission error:', error);
                resultDiv.innerHTML = `
                    <div class="result error">
                        <h4>❌ Submission Error</h4>
                        <p>${error.message}</p>
                    </div>
                `;
            });
        });

        // Load submissions
        async function loadSubmissions(pageId = null) {
            const resultDiv = document.getElementById('submissionsResult');
            resultDiv.innerHTML = '<div class="result info">Loading submissions...</div>';
            
            try {
                const formData = new FormData();
                formData.append('action', 'get_submissions');
                if (pageId) formData.append('page_id', pageId);
                
                const response = await fetch('includes/sharing_manager.php', {
                    method: 'POST',
                    body: formData
                });
                const data = await response.json();
                
                if (data.success) {
                    let html = `
                        <div class="result success">
                            <h4>✅ Loaded ${data.submissions.length} submissions (Total: ${data.total})</h4>
                        </div>
                        <table>
                            <tr>
                                <th>ID</th>
                                <th>Page</th>
                                <th>Form Data Preview</th>
                                <th>Submitted</th>
                                <th>Actions</th>
                            </tr>
                    `;
                    
                    data.submissions.forEach(submission => {
                        let preview = 'No data';
                        try {
                            if (submission.form_data) {
                                const formData = typeof submission.form_data === 'string' 
                                    ? JSON.parse(submission.form_data) 
                                    : submission.form_data;
                                const fields = Object.keys(formData).slice(0, 2);
                                preview = fields.map(field => `${field}: ${formData[field]}`).join(', ');
                                if (preview.length > 50) preview = preview.substring(0, 50) + '...';
                            }
                        } catch (e) {
                            preview = 'Invalid data format';
                        }
                        
                        html += `
                            <tr>
                                <td>${submission.id}</td>
                                <td>${submission.page_title || 'Unknown'}</td>
                                <td>${preview}</td>
                                <td>${submission.submitted_at}</td>
                                <td>
                                    <button class="btn" onclick="viewSubmissionById(${submission.id})">View</button>
                                    <button class="btn btn-danger" onclick="deleteSubmissionById(${submission.id})">Delete</button>
                                </td>
                            </tr>
                        `;
                    });
                    
                    html += '</table>';
                    resultDiv.innerHTML = html;
                } else {
                    resultDiv.innerHTML = `
                        <div class="result error">
                            <h4>❌ Failed to Load Submissions</h4>
                            <p>${data.message}</p>
                        </div>
                    `;
                }
            } catch (error) {
                console.error('Load error:', error);
                resultDiv.innerHTML = `
                    <div class="result error">
                        <h4>❌ Load Error</h4>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }

        // View specific submission
        async function viewSubmission() {
            const submissionId = document.getElementById('submissionId').value;
            if (!submissionId) {
                alert('Please enter a submission ID');
                return;
            }
            
            await viewSubmissionById(submissionId);
        }

        async function viewSubmissionById(submissionId) {
            const resultDiv = document.getElementById('operationResult');
            resultDiv.innerHTML = '<div class="result info">Loading submission details...</div>';
            
            try {
                const formData = new FormData();
                formData.append('action', 'get_submission');
                formData.append('submission_id', submissionId);
                
                const response = await fetch('includes/sharing_manager.php', {
                    method: 'POST',
                    body: formData
                });
                const data = await response.json();
                
                if (data.success) {
                    const submission = data.submission;
                    let formDataDisplay = 'No data';
                    
                    try {
                        if (submission.form_data) {
                            const formData = typeof submission.form_data === 'string' 
                                ? JSON.parse(submission.form_data) 
                                : submission.form_data;
                            formDataDisplay = JSON.stringify(formData, null, 2);
                        }
                    } catch (e) {
                        formDataDisplay = 'Invalid JSON: ' + submission.form_data;
                    }
                    
                    resultDiv.innerHTML = `
                        <div class="result success">
                            <h4>✅ Submission Details (ID: ${submission.id})</h4>
                            <p><strong>Page:</strong> ${submission.page_title || 'Unknown'}</p>
                            <p><strong>Submitted:</strong> ${submission.submitted_at}</p>
                            <p><strong>IP Address:</strong> ${submission.ip_address || 'Unknown'}</p>
                            <p><strong>Form Data:</strong></p>
                            <div class="json-data">${formDataDisplay}</div>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="result error">
                            <h4>❌ Failed to Load Submission</h4>
                            <p>${data.message}</p>
                        </div>
                    `;
                }
            } catch (error) {
                console.error('View error:', error);
                resultDiv.innerHTML = `
                    <div class="result error">
                        <h4>❌ View Error</h4>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }

        // Delete specific submission
        async function deleteSubmission() {
            const submissionId = document.getElementById('submissionId').value;
            if (!submissionId) {
                alert('Please enter a submission ID');
                return;
            }
            
            await deleteSubmissionById(submissionId);
        }

        async function deleteSubmissionById(submissionId) {
            if (!confirm(`Are you sure you want to delete submission ${submissionId}? This action cannot be undone.`)) {
                return;
            }
            
            const resultDiv = document.getElementById('operationResult');
            resultDiv.innerHTML = '<div class="result info">Deleting submission...</div>';
            
            try {
                const formData = new FormData();
                formData.append('action', 'delete_submission');
                formData.append('submission_id', submissionId);
                
                const response = await fetch('includes/sharing_manager.php', {
                    method: 'POST',
                    body: formData
                });
                const data = await response.json();
                
                if (data.success) {
                    resultDiv.innerHTML = `
                        <div class="result success">
                            <h4>✅ Submission Deleted Successfully</h4>
                            <p>Submission ${submissionId} has been removed from the database.</p>
                        </div>
                    `;
                    
                    // Clear the submission ID field
                    document.getElementById('submissionId').value = '';
                    
                    // Reload submissions if they're displayed
                    const submissionsDiv = document.getElementById('submissionsResult');
                    if (submissionsDiv.innerHTML.includes('table')) {
                        loadSubmissions();
                    }
                } else {
                    resultDiv.innerHTML = `
                        <div class="result error">
                            <h4>❌ Failed to Delete Submission</h4>
                            <p>${data.message}</p>
                        </div>
                    `;
                }
            } catch (error) {
                console.error('Delete error:', error);
                resultDiv.innerHTML = `
                    <div class="result error">
                        <h4>❌ Delete Error</h4>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }

        // Test database integration
        async function testDatabaseIntegration() {
            const resultDiv = document.getElementById('dbTestResult');
            resultDiv.innerHTML = '<div class="result info">Testing database integration...</div>';
            
            try {
                // Test the API endpoint
                const formData = new FormData();
                formData.append('action', 'get_submissions');
                
                const response = await fetch('includes/sharing_manager.php', {
                    method: 'POST',
                    body: formData
                });
                const data = await response.json();
                
                if (data.success) {
                    resultDiv.innerHTML = `
                        <div class="result success">
                            <h4>✅ Database Integration Working</h4>
                            <p><strong>API Response:</strong> Success</p>
                            <p><strong>Total Submissions:</strong> ${data.total}</p>
                            <p><strong>Submissions Loaded:</strong> ${data.submissions.length}</p>
                            <p><strong>Column Detection:</strong> Automatic (form_data/submission_data)</p>
                            <p><strong>Data Format:</strong> JSON with proper parsing</p>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="result error">
                            <h4>❌ Database Integration Issue</h4>
                            <p>${data.message}</p>
                        </div>
                    `;
                }
            } catch (error) {
                console.error('Database test error:', error);
                resultDiv.innerHTML = `
                    <div class="result error">
                        <h4>❌ Database Test Error</h4>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }
    </script>
</body>
</html>
