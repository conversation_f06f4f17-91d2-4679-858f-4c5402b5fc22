<?php
/**
 * Page Viewer for Webpage Manager
 * Displays imported HTML pages with proper asset linking
 */

// Start output buffering to prevent header issues
ob_start();

// Set error reporting to avoid warnings that break headers
error_reporting(E_ERROR | E_PARSE);

require_once 'config/database.php';

// Get page ID or share token from URL
$pageId = $_GET['id'] ?? null;
$shareToken = $_GET['token'] ?? null;
$shortCode = $_GET['s'] ?? null;

if (!$pageId && !$shareToken && !$shortCode) {
    http_response_code(400);
    die('Invalid request: Missing page identifier');
}

try {
    $database = new Database();
    $db = $database->getConnection();
    
    $page = null;
    $isSharedView = false;
    $shareInfo = null;
    
    if ($shortCode) {
        // Handle short URL redirect
        $sql = "SELECT page_id, share_token FROM page_shares WHERE short_code = ? AND is_active = 1";
        $stmt = $db->prepare($sql);
        $stmt->execute([$shortCode]);
        $share = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($share) {
            $pageId = $share['page_id'];
            $shareToken = $share['share_token'];
        } else {
            http_response_code(404);
            die('Short URL not found or expired');
        }
    }
    
    if ($shareToken) {
        // Shared page view - use aliases to avoid column name conflicts
        $sql = "SELECT ps.id as share_id, ps.*, p.id as page_id, p.* FROM page_shares ps
                JOIN pages p ON ps.page_id = p.id
                WHERE ps.share_token = ? AND ps.is_active = 1";
        $stmt = $db->prepare($sql);
        $stmt->execute([$shareToken]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$result) {
            http_response_code(404);
            die('Shared page not found or expired');
        }
        
        // Check expiration
        if ($result['expires_at'] && strtotime($result['expires_at']) < time()) {
            http_response_code(410);
            die('Shared page has expired');
        }
        
        // Check view limit
        if ($result['max_views'] && $result['view_count'] >= $result['max_views']) {
            http_response_code(410);
            die('Shared page view limit exceeded');
        }
        
        // Check password protection
        if ($result['password_hash']) {
            session_start();
            $sessionKey = 'share_auth_' . $shareToken;
            
            if (!isset($_SESSION[$sessionKey])) {
                if ($_POST['password'] ?? false) {
                    if (password_verify($_POST['password'], $result['password_hash'])) {
                        $_SESSION[$sessionKey] = true;
                    } else {
                        $error = 'Invalid password';
                    }
                }
                
                if (!isset($_SESSION[$sessionKey])) {
                    showPasswordForm($result, $error ?? null);
                    exit;
                }
            }
        }
        
        $page = $result;
        $isSharedView = true;
        $shareInfo = $result;

        // Log access using the correct share ID
        logShareAccess($db, $result['share_id'], 'view');

        // Increment view count using the correct share ID
        $sql = "UPDATE page_shares SET view_count = view_count + 1 WHERE id = ?";
        $stmt = $db->prepare($sql);
        $stmt->execute([$result['share_id']]);
        
    } else {
        // Direct page view (admin/owner view)
        // Check if status column exists (for backward compatibility)
        $sql = "SHOW COLUMNS FROM pages LIKE 'status'";
        $stmt = $db->prepare($sql);
        $stmt->execute();
        $statusColumnExists = $stmt->fetch();

        if ($statusColumnExists) {
            $sql = "SELECT * FROM pages WHERE id = ? AND status = 'active'";
        } else {
            $sql = "SELECT * FROM pages WHERE id = ?";
        }

        $stmt = $db->prepare($sql);
        $stmt->execute([$pageId]);
        $page = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$page) {
            http_response_code(404);
            die('Page not found');
        }
    }
    
    // Get associated files
    $sql = "SELECT * FROM associated_files WHERE page_id = ? ORDER BY file_type, filename";
    $stmt = $db->prepare($sql);
    $stmt->execute([$page['id']]);
    $associatedFiles = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Read and process the HTML content
    $htmlContent = file_get_contents($page['file_path']);
    if ($htmlContent === false) {
        http_response_code(500);
        die('Error reading page content');
    }
    
    // Process the HTML to fix asset paths
    $processedContent = processHtmlContent($htmlContent, $associatedFiles, $isSharedView, $shareInfo, $page);
    
    // Clear any output buffer content (warnings, etc.)
    ob_clean();

    // Set appropriate headers
    header('Content-Type: text/html; charset=UTF-8');

    // Add analytics and sharing info if it's a shared view
    if ($isSharedView) {
        $processedContent = addSharingMetadata($processedContent, $shareInfo, $page);
    }

    echo $processedContent;

    // End output buffering
    ob_end_flush();
    
} catch (PDOException $e) {
    http_response_code(500);
    die('Database error: ' . $e->getMessage());
} catch (Exception $e) {
    http_response_code(500);
    die('Server error: ' . $e->getMessage());
}

function showPasswordForm($shareInfo, $error = null) {
    ?>
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Protected Page - <?= htmlspecialchars($shareInfo['title'] ?: $shareInfo['original_filename']) ?></title>
        <style>
            body { font-family: Arial, sans-serif; background: #f5f5f5; margin: 0; padding: 20px; }
            .container { max-width: 400px; margin: 100px auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
            h1 { color: #333; text-align: center; margin-bottom: 20px; }
            .form-group { margin-bottom: 20px; }
            label { display: block; margin-bottom: 5px; font-weight: bold; }
            input[type="password"] { width: 100%; padding: 10px; border: 2px solid #ddd; border-radius: 5px; font-size: 16px; }
            button { width: 100%; padding: 12px; background: #667eea; color: white; border: none; border-radius: 5px; font-size: 16px; cursor: pointer; }
            button:hover { background: #5a67d8; }
            .error { color: #dc3545; background: #f8d7da; padding: 10px; border-radius: 5px; margin-bottom: 20px; }
            .info { color: #0c5460; background: #d1ecf1; padding: 10px; border-radius: 5px; margin-bottom: 20px; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🔒 Protected Page</h1>
            <div class="info">This page is password protected. Please enter the password to continue.</div>
            <?php if ($error): ?>
                <div class="error"><?= htmlspecialchars($error) ?></div>
            <?php endif; ?>
            <form method="POST">
                <div class="form-group">
                    <label for="password">Password:</label>
                    <input type="password" id="password" name="password" required autofocus>
                </div>
                <button type="submit">Access Page</button>
            </form>
        </div>
    </body>
    </html>
    <?php
}

function processHtmlContent($html, $associatedFiles, $isSharedView, $shareInfo, $page = null) {
    // Create a mapping of original filenames to current paths
    $fileMap = [];
    foreach ($associatedFiles as $file) {
        $fileMap[$file['original_filename']] = 'assets.php?file=' . urlencode($file['filename']);
    }
    
    // Use DOMDocument to properly parse and modify HTML
    $dom = new DOMDocument();

    // Suppress errors for malformed HTML and use UTF-8 encoding
    libxml_use_internal_errors(true);

    // Add UTF-8 meta tag if not present to ensure proper encoding
    if (strpos($html, 'charset') === false) {
        $html = str_replace('<head>', '<head><meta charset="UTF-8">', $html);
    }

    // Add mobile viewport meta tag if not present
    if (strpos($html, 'viewport') === false) {
        $html = str_replace('<head>', '<head><meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">', $html);
    }

    // Add mobile-optimized CSS
    $mobileCss = '<style>
        /* Mobile-first responsive design */
        @media (max-width: 768px) {
            body {
                font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, sans-serif !important;
                line-height: 1.6 !important;
                -webkit-font-smoothing: antialiased !important;
                -moz-osx-font-smoothing: grayscale !important;
                margin: 0 !important;
                padding: 10px !important;
            }

            /* Form enhancements for mobile */
            input, textarea, select, button {
                font-size: 16px !important; /* Prevent zoom on iOS */
                padding: 12px !important;
                border-radius: 8px !important;
                border: 1px solid #ddd !important;
                width: 100% !important;
                box-sizing: border-box !important;
                margin-bottom: 10px !important;
                -webkit-appearance: none !important;
                -moz-appearance: none !important;
                appearance: none !important;
            }

            button, input[type="submit"] {
                background: #007cba !important;
                color: white !important;
                border: none !important;
                cursor: pointer !important;
                font-weight: 500 !important;
                min-height: 44px !important;
                transition: background-color 0.2s !important;
            }

            button:hover, input[type="submit"]:hover {
                background: #005a8b !important;
            }

            button:active, input[type="submit"]:active {
                transform: scale(0.98) !important;
            }

            label {
                display: block !important;
                margin-bottom: 5px !important;
                font-weight: 500 !important;
                color: #333 !important;
                font-size: 0.9rem !important;
            }

            /* Responsive tables */
            table {
                width: 100% !important;
                border-collapse: collapse !important;
                font-size: 0.85rem !important;
                overflow-x: auto !important;
                display: block !important;
                white-space: nowrap !important;
            }

            th, td {
                padding: 8px 4px !important;
                border: 1px solid #ddd !important;
                text-align: left !important;
            }

            th {
                background: #f8f9fa !important;
                font-weight: 600 !important;
            }

            /* Improve readability */
            p, div, span {
                line-height: 1.5 !important;
            }

            h1, h2, h3, h4, h5, h6 {
                line-height: 1.3 !important;
                margin-bottom: 0.5em !important;
            }

            /* Touch-friendly links */
            a {
                min-height: 44px !important;
                display: inline-block !important;
                padding: 8px !important;
            }
        }

        /* Very small screens */
        @media (max-width: 480px) {
            body {
                padding: 5px !important;
            }
            input, textarea, select, button {
                padding: 10px !important;
                font-size: 16px !important;
            }
        }
    </style>';

    // Insert mobile CSS before closing head tag
    $html = str_replace('</head>', $mobileCss . '</head>', $html);

    $dom->loadHTML('<?xml encoding="UTF-8">' . $html, LIBXML_HTML_NOIMPLIED | LIBXML_HTML_NODEFDTD);

    // Clear any libxml errors
    libxml_clear_errors();
    
    // Fix CSS links
    $links = $dom->getElementsByTagName('link');
    foreach ($links as $link) {
        $href = $link->getAttribute('href');
        if ($href && isset($fileMap[basename($href)])) {
            $link->setAttribute('href', $fileMap[basename($href)]);
        }
    }
    
    // Fix script sources
    $scripts = $dom->getElementsByTagName('script');
    foreach ($scripts as $script) {
        $src = $script->getAttribute('src');
        if ($src && isset($fileMap[basename($src)])) {
            $script->setAttribute('src', $fileMap[basename($src)]);
        }
    }
    
    // Fix image sources
    $images = $dom->getElementsByTagName('img');
    foreach ($images as $img) {
        $src = $img->getAttribute('src');
        if ($src && isset($fileMap[basename($src)])) {
            $img->setAttribute('src', $fileMap[basename($src)]);
        }
    }
    
    // Handle forms for shared views and direct views
    if ($isSharedView) {
        if (!$shareInfo['show_forms']) {
            // Remove forms if not allowed
            $forms = $dom->getElementsByTagName('form');
            for ($i = $forms->length - 1; $i >= 0; $i--) {
                $form = $forms->item($i);
                $form->parentNode->removeChild($form);
            }
        } else {
            // Enhance forms for data collection
            enhanceFormsForSubmission($dom, $shareInfo, $page);
        }
    } else {
        // For direct views, still enable data collection but without share tracking
        enhanceFormsForDataCollection($dom, $page);
    }

    return $dom->saveHTML();
}

function enhanceFormsForSubmission($dom, $shareInfo, $page) {
    // Ensure we have required data
    if (!$shareInfo || !isset($shareInfo['share_token'])) {
        return;
    }

    $pageId = $page ? $page['id'] : ($shareInfo['page_id'] ?? null);
    if (!$pageId) {
        return;
    }

    // Get all forms in the document
    $forms = $dom->getElementsByTagName('form');

    foreach ($forms as $form) {
        // Set form action to our submission handler
        $form->setAttribute('action', 'enhanced_submit_form.php');
        $form->setAttribute('method', 'POST');

        // Add hidden fields for tracking
        $redirectUrl = $shareInfo['redirect_url'] ?: $form->getAttribute('data-redirect') ?: $form->getAttribute('action') ?: '';
        $hiddenFields = [
            '_share_token' => $shareInfo['share_token'],
            '_page_id' => $pageId,
            '_form_name' => $form->getAttribute('name') ?: 'unnamed_form',
            '_redirect_url' => $redirectUrl,
            '_original_action' => $form->getAttribute('action') ?: ''
        ];

        foreach ($hiddenFields as $name => $value) {
            $hiddenInput = $dom->createElement('input');
            $hiddenInput->setAttribute('type', 'hidden');
            $hiddenInput->setAttribute('name', $name);
            $hiddenInput->setAttribute('value', (string)$value); // Cast to string to avoid null warnings
            $form->appendChild($hiddenInput);
        }

        // Add JavaScript for AJAX submission
        $script = $dom->createElement('script');
        $script->textContent = "
            document.addEventListener('DOMContentLoaded', function() {
                const forms = document.querySelectorAll('form[action=\"enhanced_submit_form.php\"]');
                forms.forEach(function(form) {
                    form.addEventListener('submit', function(e) {
                        e.preventDefault();

                        const formData = new FormData(form);

                        fetch('enhanced_submit_form.php', {
                            method: 'POST',
                            body: formData
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                // Show success message
                                alert(data.message || 'Form submitted successfully!');

                                // Reset form
                                form.reset();

                                // Handle redirect if specified
                                if (data.redirect_url) {
                                    // Small delay to ensure user sees the success message
                                    setTimeout(function() {
                                        window.location.href = data.redirect_url;
                                    }, 1000);
                                }
                            } else {
                                alert('Error: ' + data.message);
                            }
                        })
                        .catch(error => {
                            console.error('Error:', error);
                            alert('An error occurred while submitting the form.');
                        });
                    });
                });
            });
        ";

        // Add script to head or body
        $head = $dom->getElementsByTagName('head')->item(0);
        if ($head) {
            $head->appendChild($script);
        } else {
            $body = $dom->getElementsByTagName('body')->item(0);
            if ($body) {
                $body->appendChild($script);
            }
        }

        break; // Only add script once
    }

    // Also handle standalone inputs (inputs outside forms)
    $xpath = new DOMXPath($dom);
    $standaloneInputs = $xpath->query('//input[not(ancestor::form)] | //textarea[not(ancestor::form)] | //select[not(ancestor::form)]');

    if ($standaloneInputs->length > 0) {
        // Create a wrapper form for standalone inputs
        $wrapperForm = $dom->createElement('form');
        $wrapperForm->setAttribute('action', 'submit_form.php');
        $wrapperForm->setAttribute('method', 'POST');
        $wrapperForm->setAttribute('style', 'display: contents;'); // Don't affect layout

        // Add hidden fields
        $hiddenFields = [
            '_share_token' => $shareInfo['share_token'],
            '_page_id' => $pageId,
            '_form_name' => 'standalone_inputs',
            '_redirect_url' => $shareInfo['redirect_url'] ?: ''
        ];

        foreach ($hiddenFields as $name => $value) {
            $hiddenInput = $dom->createElement('input');
            $hiddenInput->setAttribute('type', 'hidden');
            $hiddenInput->setAttribute('name', $name);
            $hiddenInput->setAttribute('value', (string)$value); // Cast to string to avoid null warnings
            $wrapperForm->appendChild($hiddenInput);
        }

        // Add submit button for standalone inputs
        $submitButton = $dom->createElement('button');
        $submitButton->setAttribute('type', 'submit');
        $submitButton->setAttribute('style', 'margin: 10px 0; padding: 10px 20px; background: #667eea; color: white; border: none; border-radius: 5px; cursor: pointer;');
        $submitButton->textContent = 'Submit';
        $wrapperForm->appendChild($submitButton);

        // Insert wrapper form at the end of body
        $body = $dom->getElementsByTagName('body')->item(0);
        if ($body) {
            $body->appendChild($wrapperForm);
        }
    }
}

function addSharingMetadata($html, $shareInfo, $page) {
    // Add meta tags and analytics
    $metaTags = '';
    if ($shareInfo['title']) {
        $metaTags .= '<meta property="og:title" content="' . htmlspecialchars($shareInfo['title']) . '">' . "\n";
    }
    if ($shareInfo['description']) {
        $metaTags .= '<meta property="og:description" content="' . htmlspecialchars($shareInfo['description']) . '">' . "\n";
    }
    
    // Add sharing info banner if metadata should be shown
    $banner = '';
    if ($shareInfo['show_metadata']) {
        $banner = '<div style="background: #667eea; color: white; padding: 10px; text-align: center; font-family: Arial, sans-serif;">
            <strong>Shared Page:</strong> ' . htmlspecialchars($shareInfo['title'] ?: $page['title']) . '
            <span style="margin-left: 20px; font-size: 0.9em;">Views: ' . $shareInfo['view_count'] . '</span>
        </div>';
    }
    
    // Insert meta tags in head and banner at top of body
    $html = str_replace('</head>', $metaTags . '</head>', $html);
    $html = str_replace('<body>', '<body>' . $banner, $html);
    
    return $html;
}

function enhanceFormsForDataCollection($dom, $page) {
    // For direct page views (not shared), enable data collection
    if (!$page || !isset($page['id'])) {
        return;
    }

    $pageId = $page['id'];

    // Get all forms in the document
    $forms = $dom->getElementsByTagName('form');

    foreach ($forms as $form) {
        // Set form action to our enhanced submission handler
        $form->setAttribute('action', 'enhanced_submit_form.php');
        $form->setAttribute('method', 'POST');

        // Add hidden fields for tracking
        $redirectUrl = $form->getAttribute('data-redirect') ?: $form->getAttribute('action') ?: '';
        $hiddenFields = [
            '_page_id' => $pageId,
            '_form_name' => $form->getAttribute('name') ?: 'unnamed_form',
            '_redirect_url' => $redirectUrl,
            '_original_action' => $form->getAttribute('action') ?: ''
        ];

        foreach ($hiddenFields as $name => $value) {
            $hidden = $dom->createElement('input');
            $hidden->setAttribute('type', 'hidden');
            $hidden->setAttribute('name', $name);
            $hidden->setAttribute('value', $value);
            $form->insertBefore($hidden, $form->firstChild);
        }

        // Add JavaScript for AJAX submission (only once)
        static $scriptAdded = false;
        if (!$scriptAdded) {
            $script = $dom->createElement('script');
            $script->textContent = "
                document.addEventListener('DOMContentLoaded', function() {
                    const forms = document.querySelectorAll('form[action=\"enhanced_submit_form.php\"]');
                    forms.forEach(function(form) {
                        form.addEventListener('submit', function(e) {
                            e.preventDefault();

                            const formData = new FormData(form);

                            fetch('enhanced_submit_form.php', {
                                method: 'POST',
                                body: formData
                            })
                            .then(response => response.json())
                            .then(data => {
                                if (data.success) {
                                    // Show success message
                                    alert(data.message || 'Form submitted successfully!');

                                    // Reset form
                                    form.reset();

                                    // Handle redirect if specified
                                    if (data.redirect_url) {
                                        // Small delay to ensure user sees the success message
                                        setTimeout(function() {
                                            window.location.href = data.redirect_url;
                                        }, 1000);
                                    }
                                } else {
                                    alert('Error: ' + data.message);
                                }
                            })
                            .catch(error => {
                                console.error('Error:', error);
                                alert('An error occurred while submitting the form.');
                            });
                        });
                    });
                });
            ";

            // Add script to head or body
            $head = $dom->getElementsByTagName('head')->item(0);
            if ($head) {
                $head->appendChild($script);
            } else {
                $body = $dom->getElementsByTagName('body')->item(0);
                if ($body) {
                    $body->appendChild($script);
                }
            }
            $scriptAdded = true;
        }
    }

    // Handle standalone inputs (not in forms)
    $xpath = new DOMXPath($dom);
    $standaloneInputs = $xpath->query('//input[not(ancestor::form)] | //textarea[not(ancestor::form)] | //select[not(ancestor::form)]');

    if ($standaloneInputs->length > 0) {
        // Create a wrapper form for standalone inputs
        $wrapperForm = $dom->createElement('form');
        $wrapperForm->setAttribute('action', 'enhanced_submit_form.php');
        $wrapperForm->setAttribute('method', 'POST');
        $wrapperForm->setAttribute('style', 'display: contents;');

        // Add hidden fields
        $hiddenFields = [
            '_page_id' => $pageId,
            '_form_name' => 'standalone_inputs',
            '_original_action' => ''
        ];

        foreach ($hiddenFields as $name => $value) {
            $hidden = $dom->createElement('input');
            $hidden->setAttribute('type', 'hidden');
            $hidden->setAttribute('name', $name);
            $hidden->setAttribute('value', $value);
            $wrapperForm->appendChild($hidden);
        }

        // Move standalone inputs into the wrapper form
        foreach ($standaloneInputs as $input) {
            $input->parentNode->insertBefore($wrapperForm, $input);
            $wrapperForm->appendChild($input);
        }
    }
}

function logShareAccess($db, $shareId, $accessType = 'view') {
    try {
        // First verify the share exists to prevent foreign key constraint violation
        $sql = "SELECT id FROM page_shares WHERE id = ? AND is_active = 1";
        $stmt = $db->prepare($sql);
        $stmt->execute([$shareId]);

        if (!$stmt->fetch()) {
            // Share doesn't exist or is inactive, don't log
            error_log("Attempted to log access for non-existent share ID: $shareId");
            return false;
        }

        $ip = $_SERVER['REMOTE_ADDR'] ?? '';
        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
        $referrer = $_SERVER['HTTP_REFERER'] ?? '';

        $sql = "INSERT INTO share_access_log (share_id, ip_address, user_agent, referrer, access_type)
                VALUES (?, ?, ?, ?, ?)";
        $stmt = $db->prepare($sql);
        $stmt->execute([$shareId, $ip, $userAgent, $referrer, $accessType]);

        return true;
    } catch (PDOException $e) {
        // Log the error but don't break the page view
        error_log("Failed to log share access: " . $e->getMessage());
        return false;
    }
}
