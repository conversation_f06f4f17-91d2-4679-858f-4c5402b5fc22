<?php
/**
 * Page Viewer for Webpage Manager
 * Displays imported HTML pages with proper asset linking
 */

require_once 'config/database.php';

// Get page ID or share token from URL
$pageId = $_GET['id'] ?? null;
$shareToken = $_GET['token'] ?? null;
$shortCode = $_GET['s'] ?? null;

if (!$pageId && !$shareToken && !$shortCode) {
    http_response_code(400);
    die('Invalid request: Missing page identifier');
}

try {
    $database = new Database();
    $db = $database->getConnection();
    
    $page = null;
    $isSharedView = false;
    $shareInfo = null;
    
    if ($shortCode) {
        // Handle short URL redirect
        $sql = "SELECT page_id, share_token FROM page_shares WHERE short_code = ? AND is_active = 1";
        $stmt = $db->prepare($sql);
        $stmt->execute([$shortCode]);
        $share = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($share) {
            $pageId = $share['page_id'];
            $shareToken = $share['share_token'];
        } else {
            http_response_code(404);
            die('Short URL not found or expired');
        }
    }
    
    if ($shareToken) {
        // Shared page view
        $sql = "SELECT ps.*, p.* FROM page_shares ps 
                JOIN pages p ON ps.page_id = p.id 
                WHERE ps.share_token = ? AND ps.is_active = 1";
        $stmt = $db->prepare($sql);
        $stmt->execute([$shareToken]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$result) {
            http_response_code(404);
            die('Shared page not found or expired');
        }
        
        // Check expiration
        if ($result['expires_at'] && strtotime($result['expires_at']) < time()) {
            http_response_code(410);
            die('Shared page has expired');
        }
        
        // Check view limit
        if ($result['max_views'] && $result['view_count'] >= $result['max_views']) {
            http_response_code(410);
            die('Shared page view limit exceeded');
        }
        
        // Check password protection
        if ($result['password_hash']) {
            session_start();
            $sessionKey = 'share_auth_' . $shareToken;
            
            if (!isset($_SESSION[$sessionKey])) {
                if ($_POST['password'] ?? false) {
                    if (password_verify($_POST['password'], $result['password_hash'])) {
                        $_SESSION[$sessionKey] = true;
                    } else {
                        $error = 'Invalid password';
                    }
                }
                
                if (!isset($_SESSION[$sessionKey])) {
                    showPasswordForm($result, $error ?? null);
                    exit;
                }
            }
        }
        
        $page = $result;
        $isSharedView = true;
        $shareInfo = $result;
        
        // Log access
        logShareAccess($db, $result['id'], 'view');
        
        // Increment view count
        $sql = "UPDATE page_shares SET view_count = view_count + 1 WHERE id = ?";
        $stmt = $db->prepare($sql);
        $stmt->execute([$result['id']]);
        
    } else {
        // Direct page view (admin/owner view)
        // Check if status column exists (for backward compatibility)
        $sql = "SHOW COLUMNS FROM pages LIKE 'status'";
        $stmt = $db->prepare($sql);
        $stmt->execute();
        $statusColumnExists = $stmt->fetch();

        if ($statusColumnExists) {
            $sql = "SELECT * FROM pages WHERE id = ? AND status = 'active'";
        } else {
            $sql = "SELECT * FROM pages WHERE id = ?";
        }

        $stmt = $db->prepare($sql);
        $stmt->execute([$pageId]);
        $page = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$page) {
            http_response_code(404);
            die('Page not found');
        }
    }
    
    // Get associated files
    $sql = "SELECT * FROM associated_files WHERE page_id = ? ORDER BY file_type, filename";
    $stmt = $db->prepare($sql);
    $stmt->execute([$page['id']]);
    $associatedFiles = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Read and process the HTML content
    $htmlContent = file_get_contents($page['file_path']);
    if ($htmlContent === false) {
        http_response_code(500);
        die('Error reading page content');
    }
    
    // Process the HTML to fix asset paths
    $processedContent = processHtmlContent($htmlContent, $associatedFiles, $isSharedView, $shareInfo);
    
    // Set appropriate headers
    header('Content-Type: text/html; charset=UTF-8');
    
    // Add analytics and sharing info if it's a shared view
    if ($isSharedView) {
        $processedContent = addSharingMetadata($processedContent, $shareInfo, $page);
    }
    
    echo $processedContent;
    
} catch (PDOException $e) {
    http_response_code(500);
    die('Database error: ' . $e->getMessage());
} catch (Exception $e) {
    http_response_code(500);
    die('Server error: ' . $e->getMessage());
}

function showPasswordForm($shareInfo, $error = null) {
    ?>
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Protected Page - <?= htmlspecialchars($shareInfo['title'] ?: $shareInfo['original_filename']) ?></title>
        <style>
            body { font-family: Arial, sans-serif; background: #f5f5f5; margin: 0; padding: 20px; }
            .container { max-width: 400px; margin: 100px auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
            h1 { color: #333; text-align: center; margin-bottom: 20px; }
            .form-group { margin-bottom: 20px; }
            label { display: block; margin-bottom: 5px; font-weight: bold; }
            input[type="password"] { width: 100%; padding: 10px; border: 2px solid #ddd; border-radius: 5px; font-size: 16px; }
            button { width: 100%; padding: 12px; background: #667eea; color: white; border: none; border-radius: 5px; font-size: 16px; cursor: pointer; }
            button:hover { background: #5a67d8; }
            .error { color: #dc3545; background: #f8d7da; padding: 10px; border-radius: 5px; margin-bottom: 20px; }
            .info { color: #0c5460; background: #d1ecf1; padding: 10px; border-radius: 5px; margin-bottom: 20px; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🔒 Protected Page</h1>
            <div class="info">This page is password protected. Please enter the password to continue.</div>
            <?php if ($error): ?>
                <div class="error"><?= htmlspecialchars($error) ?></div>
            <?php endif; ?>
            <form method="POST">
                <div class="form-group">
                    <label for="password">Password:</label>
                    <input type="password" id="password" name="password" required autofocus>
                </div>
                <button type="submit">Access Page</button>
            </form>
        </div>
    </body>
    </html>
    <?php
}

function processHtmlContent($html, $associatedFiles, $isSharedView, $shareInfo) {
    // Create a mapping of original filenames to current paths
    $fileMap = [];
    foreach ($associatedFiles as $file) {
        $fileMap[$file['original_filename']] = 'assets.php?file=' . urlencode($file['filename']);
    }
    
    // Use DOMDocument to properly parse and modify HTML
    $dom = new DOMDocument();
    $dom->loadHTML($html, LIBXML_HTML_NOIMPLIED | LIBXML_HTML_NODEFDTD);
    
    // Fix CSS links
    $links = $dom->getElementsByTagName('link');
    foreach ($links as $link) {
        $href = $link->getAttribute('href');
        if ($href && isset($fileMap[basename($href)])) {
            $link->setAttribute('href', $fileMap[basename($href)]);
        }
    }
    
    // Fix script sources
    $scripts = $dom->getElementsByTagName('script');
    foreach ($scripts as $script) {
        $src = $script->getAttribute('src');
        if ($src && isset($fileMap[basename($src)])) {
            $script->setAttribute('src', $fileMap[basename($src)]);
        }
    }
    
    // Fix image sources
    $images = $dom->getElementsByTagName('img');
    foreach ($images as $img) {
        $src = $img->getAttribute('src');
        if ($src && isset($fileMap[basename($src)])) {
            $img->setAttribute('src', $fileMap[basename($src)]);
        }
    }
    
    // Handle forms for shared views
    if ($isSharedView) {
        if (!$shareInfo['show_forms']) {
            // Remove forms if not allowed
            $forms = $dom->getElementsByTagName('form');
            for ($i = $forms->length - 1; $i >= 0; $i--) {
                $form = $forms->item($i);
                $form->parentNode->removeChild($form);
            }
        } else {
            // Enhance forms for data collection
            enhanceFormsForSubmission($dom, $shareInfo, $page);
        }
    }

    return $dom->saveHTML();
}

function enhanceFormsForSubmission($dom, $shareInfo, $page) {
    // Get all forms in the document
    $forms = $dom->getElementsByTagName('form');

    foreach ($forms as $form) {
        // Set form action to our submission handler
        $form->setAttribute('action', 'submit_form.php');
        $form->setAttribute('method', 'POST');

        // Add hidden fields for tracking
        $hiddenFields = [
            '_share_token' => $shareInfo['share_token'],
            '_page_id' => $page['id'],
            '_form_name' => $form->getAttribute('name') ?: 'unnamed_form'
        ];

        foreach ($hiddenFields as $name => $value) {
            $hiddenInput = $dom->createElement('input');
            $hiddenInput->setAttribute('type', 'hidden');
            $hiddenInput->setAttribute('name', $name);
            $hiddenInput->setAttribute('value', $value);
            $form->appendChild($hiddenInput);
        }

        // Add JavaScript for AJAX submission
        $script = $dom->createElement('script');
        $script->textContent = "
            document.addEventListener('DOMContentLoaded', function() {
                const forms = document.querySelectorAll('form[action=\"submit_form.php\"]');
                forms.forEach(function(form) {
                    form.addEventListener('submit', function(e) {
                        e.preventDefault();

                        const formData = new FormData(form);

                        fetch('submit_form.php', {
                            method: 'POST',
                            body: formData
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                alert('Form submitted successfully!');
                                form.reset();
                            } else {
                                alert('Error: ' + data.message);
                            }
                        })
                        .catch(error => {
                            console.error('Error:', error);
                            alert('An error occurred while submitting the form.');
                        });
                    });
                });
            });
        ";

        // Add script to head or body
        $head = $dom->getElementsByTagName('head')->item(0);
        if ($head) {
            $head->appendChild($script);
        } else {
            $body = $dom->getElementsByTagName('body')->item(0);
            if ($body) {
                $body->appendChild($script);
            }
        }

        break; // Only add script once
    }

    // Also handle standalone inputs (inputs outside forms)
    $xpath = new DOMXPath($dom);
    $standaloneInputs = $xpath->query('//input[not(ancestor::form)] | //textarea[not(ancestor::form)] | //select[not(ancestor::form)]');

    if ($standaloneInputs->length > 0) {
        // Create a wrapper form for standalone inputs
        $wrapperForm = $dom->createElement('form');
        $wrapperForm->setAttribute('action', 'submit_form.php');
        $wrapperForm->setAttribute('method', 'POST');
        $wrapperForm->setAttribute('style', 'display: contents;'); // Don't affect layout

        // Add hidden fields
        $hiddenFields = [
            '_share_token' => $shareInfo['share_token'],
            '_page_id' => $page['id'],
            '_form_name' => 'standalone_inputs'
        ];

        foreach ($hiddenFields as $name => $value) {
            $hiddenInput = $dom->createElement('input');
            $hiddenInput->setAttribute('type', 'hidden');
            $hiddenInput->setAttribute('name', $name);
            $hiddenInput->setAttribute('value', $value);
            $wrapperForm->appendChild($hiddenInput);
        }

        // Add submit button for standalone inputs
        $submitButton = $dom->createElement('button');
        $submitButton->setAttribute('type', 'submit');
        $submitButton->setAttribute('style', 'margin: 10px 0; padding: 10px 20px; background: #667eea; color: white; border: none; border-radius: 5px; cursor: pointer;');
        $submitButton->textContent = 'Submit';
        $wrapperForm->appendChild($submitButton);

        // Insert wrapper form at the end of body
        $body = $dom->getElementsByTagName('body')->item(0);
        if ($body) {
            $body->appendChild($wrapperForm);
        }
    }
}

function addSharingMetadata($html, $shareInfo, $page) {
    // Add meta tags and analytics
    $metaTags = '';
    if ($shareInfo['title']) {
        $metaTags .= '<meta property="og:title" content="' . htmlspecialchars($shareInfo['title']) . '">' . "\n";
    }
    if ($shareInfo['description']) {
        $metaTags .= '<meta property="og:description" content="' . htmlspecialchars($shareInfo['description']) . '">' . "\n";
    }
    
    // Add sharing info banner if metadata should be shown
    $banner = '';
    if ($shareInfo['show_metadata']) {
        $banner = '<div style="background: #667eea; color: white; padding: 10px; text-align: center; font-family: Arial, sans-serif;">
            <strong>Shared Page:</strong> ' . htmlspecialchars($shareInfo['title'] ?: $page['title']) . '
            <span style="margin-left: 20px; font-size: 0.9em;">Views: ' . $shareInfo['view_count'] . '</span>
        </div>';
    }
    
    // Insert meta tags in head and banner at top of body
    $html = str_replace('</head>', $metaTags . '</head>', $html);
    $html = str_replace('<body>', '<body>' . $banner, $html);
    
    return $html;
}

function logShareAccess($db, $shareId, $accessType = 'view') {
    $ip = $_SERVER['REMOTE_ADDR'] ?? '';
    $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
    $referrer = $_SERVER['HTTP_REFERER'] ?? '';
    
    $sql = "INSERT INTO share_access_log (share_id, ip_address, user_agent, referrer, access_type) 
            VALUES (?, ?, ?, ?, ?)";
    $stmt = $db->prepare($sql);
    $stmt->execute([$shareId, $ip, $userAgent, $referrer, $accessType]);
}
