<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Main App - Form Submissions</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .container { max-width: 1200px; margin: 0 auto; }
        .btn { background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin: 5px; }
        .btn-danger { background: #dc3545; }
        .btn-success { background: #28a745; }
        .btn-icon { background: transparent; border: none; padding: 8px; border-radius: 4px; cursor: pointer; margin: 0 2px; }
        .btn-icon.btn-danger { color: #dc3545; }
        .btn-icon:hover { background: #f0f0f0; }
        .result { margin: 10px 0; padding: 15px; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .warning { background: #fff3cd; color: #856404; }
        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        th, td { border: 1px solid #ddd; padding: 12px; text-align: left; }
        th { background: #f8f9fa; font-weight: 600; }
        .submission-row { cursor: pointer; }
        .submission-row:hover { background: #f8f9fa; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 5px; overflow-x: auto; }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="container">
        <h1>Debug Main App - Form Submissions</h1>
        
        <div class="result warning">
            <h3>🔍 Debugging Form Data Viewing and Deletion</h3>
            <p>This page tests the exact same functionality as the main application to identify issues.</p>
        </div>

        <div style="margin: 20px 0;">
            <button onclick="loadSubmissions()" class="btn btn-success">Load Submissions</button>
            <button onclick="testAPI()" class="btn">Test API</button>
            <button onclick="clearResults()" class="btn">Clear Results</button>
        </div>

        <div id="debug-output"></div>
        <div id="submissions-container"></div>
    </div>

    <script>
        // Debug logging
        function debugLog(message, data = null) {
            console.log('[DEBUG]', message, data);
            const output = document.getElementById('debug-output');
            const logEntry = document.createElement('div');
            logEntry.style.cssText = 'background: #f8f9fa; padding: 10px; margin: 5px 0; border-radius: 5px; font-family: monospace;';
            logEntry.innerHTML = `<strong>[${new Date().toLocaleTimeString()}]</strong> ${message}`;
            if (data) {
                logEntry.innerHTML += `<pre>${JSON.stringify(data, null, 2)}</pre>`;
            }
            output.appendChild(logEntry);
        }

        // Test API directly
        async function testAPI() {
            debugLog('Testing API endpoint...');
            
            try {
                const formData = new FormData();
                formData.append('action', 'get_submissions');
                
                debugLog('Sending request to includes/sharing_manager.php');
                
                const response = await fetch('includes/sharing_manager.php', {
                    method: 'POST',
                    body: formData
                });
                
                debugLog('Response received', {
                    status: response.status,
                    statusText: response.statusText,
                    headers: Object.fromEntries(response.headers.entries())
                });
                
                const text = await response.text();
                debugLog('Raw response text', text);
                
                try {
                    const data = JSON.parse(text);
                    debugLog('Parsed JSON response', data);
                    
                    if (data.success) {
                        debugLog('✅ API Success', {
                            total: data.total,
                            submissions_count: data.submissions.length,
                            pages_count: data.pages ? data.pages.length : 0,
                            forms_count: data.forms ? data.forms.length : 0
                        });
                        
                        if (data.submissions.length > 0) {
                            debugLog('Sample submission', data.submissions[0]);
                        }
                    } else {
                        debugLog('❌ API Error', data.message);
                    }
                } catch (parseError) {
                    debugLog('❌ JSON Parse Error', parseError.message);
                }
                
            } catch (error) {
                debugLog('❌ Network Error', error.message);
            }
        }

        // Load submissions (same as main app)
        async function loadSubmissions() {
            debugLog('Loading form submissions...');
            
            try {
                const formData = new FormData();
                formData.append('action', 'get_submissions');
                
                const response = await fetch('includes/sharing_manager.php', {
                    method: 'POST',
                    body: formData
                });
                const result = await response.json();

                debugLog('Load submissions response', result);

                if (result.success) {
                    displayFormSubmissions(result.submissions);
                    debugLog('✅ Submissions loaded successfully', {
                        count: result.submissions.length,
                        total: result.total
                    });
                } else {
                    debugLog('❌ Failed to load submissions', result.message);
                    showError(result.message || 'Failed to load form submissions');
                }
            } catch (error) {
                debugLog('❌ Load submissions error', error.message);
                showError('Failed to load form submissions');
            }
        }

        // Display submissions (same as main app)
        function displayFormSubmissions(submissions) {
            debugLog('Displaying submissions', { count: submissions.length });
            
            const container = document.getElementById('submissions-container');

            if (!submissions || submissions.length === 0) {
                container.innerHTML = `
                    <div class="result warning">
                        <h3>No Form Submissions</h3>
                        <p>Form submissions from shared pages will appear here.</p>
                    </div>
                `;
                return;
            }

            const table = `
                <div class="result success">
                    <h3>✅ Found ${submissions.length} submissions</h3>
                </div>
                <table class="submissions-table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Date</th>
                            <th>Page</th>
                            <th>Form</th>
                            <th>Source</th>
                            <th>Data Preview</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${submissions.map(submission => {
                            debugLog(`Processing submission ${submission.id}`, submission);
                            
                            const preview = formatSubmissionPreview(submission.form_data);
                            debugLog(`Preview for submission ${submission.id}`, preview);
                            
                            return `
                                <tr class="submission-row" onclick="showSubmissionDetails(${submission.id})">
                                    <td>${submission.id}</td>
                                    <td class="submission-date">${new Date(submission.submitted_at).toLocaleString()}</td>
                                    <td>${submission.page_title || submission.page_filename || 'Unknown'}</td>
                                    <td>${submission.form_name || 'Unnamed Form'}</td>
                                    <td>
                                        <div class="submission-source">
                                            <span class="source-badge ${submission.share_id ? 'shared' : 'direct'}">
                                                ${submission.share_id ? 'Shared' : 'Direct'}
                                            </span>
                                        </div>
                                    </td>
                                    <td class="submission-data">${preview}</td>
                                    <td>
                                        <button class="btn-icon" onclick="event.stopPropagation(); showSubmissionDetails(${submission.id})" title="View Details">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn-icon" onclick="event.stopPropagation(); exportSingleSubmission(${submission.id})" title="Export">
                                            <i class="fas fa-download"></i>
                                        </button>
                                        <button class="btn-icon btn-danger" onclick="event.stopPropagation(); deleteSubmission(${submission.id})" title="Delete">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </td>
                                </tr>
                            `;
                        }).join('')}
                    </tbody>
                </table>
            `;

            container.innerHTML = table;
            debugLog('✅ Table rendered successfully');
        }

        // Format submission preview (same as main app)
        function formatSubmissionPreview(formDataJson) {
            debugLog('Formatting preview for data', formDataJson);
            
            try {
                // Handle null, undefined, or empty values
                if (!formDataJson || formDataJson === 'null' || formDataJson === '') {
                    debugLog('No data found');
                    return 'No data';
                }
                
                // If it's already an object, use it directly
                let data;
                if (typeof formDataJson === 'object') {
                    data = formDataJson;
                    debugLog('Data is already an object', data);
                } else {
                    // Try to parse as JSON
                    data = JSON.parse(formDataJson);
                    debugLog('Parsed JSON data', data);
                }
                
                // Check if data is empty
                if (!data || typeof data !== 'object' || Object.keys(data).length === 0) {
                    debugLog('Empty or invalid data structure', data);
                    return 'Empty form';
                }
                
                const fields = Object.keys(data).slice(0, 3); // Show first 3 fields
                const preview = fields.map(field => {
                    const value = data[field];
                    const displayValue = value && value.length > 20 ? value.substring(0, 20) + '...' : value;
                    return `${field}: ${displayValue}`;
                }).join(', ');
                
                const result = preview.length > 50 ? preview.substring(0, 50) + '...' : preview;
                debugLog('Generated preview', result);
                return result;
            } catch (error) {
                debugLog('❌ Error formatting submission preview', { error: error.message, data: formDataJson });
                return 'Invalid data format';
            }
        }

        // Show submission details
        async function showSubmissionDetails(submissionId) {
            debugLog(`Showing details for submission ${submissionId}`);
            
            try {
                const formData = new FormData();
                formData.append('action', 'get_submission');
                formData.append('submission_id', submissionId);
                
                const response = await fetch('includes/sharing_manager.php', {
                    method: 'POST',
                    body: formData
                });
                const result = await response.json();

                debugLog('Submission details response', result);

                if (result.success && result.submission) {
                    displaySubmissionModal(result.submission);
                } else {
                    debugLog('❌ Failed to load submission details', result.message);
                    showError(result.message || 'Failed to load submission details');
                }
            } catch (error) {
                debugLog('❌ Error loading submission details', error.message);
                showError('Failed to load submission details');
            }
        }

        // Delete submission
        async function deleteSubmission(submissionId) {
            debugLog(`Attempting to delete submission ${submissionId}`);
            
            if (!confirm('Are you sure you want to delete this form submission? This action cannot be undone.')) {
                debugLog('Delete cancelled by user');
                return;
            }
            
            try {
                const formData = new FormData();
                formData.append('action', 'delete_submission');
                formData.append('submission_id', submissionId);
                
                const response = await fetch('includes/sharing_manager.php', {
                    method: 'POST',
                    body: formData
                });
                const result = await response.json();

                debugLog('Delete submission response', result);

                if (result.success) {
                    debugLog('✅ Submission deleted successfully');
                    showSuccess('Submission deleted successfully');
                    loadSubmissions(); // Reload the submissions list
                } else {
                    debugLog('❌ Failed to delete submission', result.message);
                    showError(result.message || 'Failed to delete submission');
                }
            } catch (error) {
                debugLog('❌ Error deleting submission', error.message);
                showError('Failed to delete submission');
            }
        }

        // Display modal (simplified)
        function displaySubmissionModal(submission) {
            debugLog('Displaying submission modal', submission);
            
            let formData;
            try {
                if (typeof submission.form_data === 'object') {
                    formData = submission.form_data;
                } else if (submission.form_data) {
                    formData = JSON.parse(submission.form_data);
                } else {
                    formData = {};
                }
            } catch (error) {
                debugLog('❌ Error parsing form data for modal', error.message);
                formData = { error: 'Unable to parse form data', raw_data: submission.form_data };
            }

            alert(`Submission Details:\n\nID: ${submission.id}\nDate: ${submission.submitted_at}\nData: ${JSON.stringify(formData, null, 2)}`);
        }

        // Utility functions
        function showError(message) {
            const container = document.getElementById('submissions-container');
            container.innerHTML = `<div class="result error"><h3>❌ Error</h3><p>${message}</p></div>`;
        }

        function showSuccess(message) {
            const container = document.getElementById('submissions-container');
            container.innerHTML = `<div class="result success"><h3>✅ Success</h3><p>${message}</p></div>`;
        }

        function exportSingleSubmission(submissionId) {
            debugLog(`Export requested for submission ${submissionId}`);
            alert(`Export functionality for submission ${submissionId} (not implemented in debug version)`);
        }

        function clearResults() {
            document.getElementById('debug-output').innerHTML = '';
            document.getElementById('submissions-container').innerHTML = '';
        }

        // Auto-load on page load
        window.addEventListener('load', function() {
            debugLog('Page loaded, testing API...');
            testAPI();
        });
    </script>
</body>
</html>
