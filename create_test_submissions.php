<?php
/**
 * Create Test Submissions for Testing
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once 'config/database.php';

echo "<h1>Create Test Submissions</h1>";

try {
    $database = new Database();
    $db = $database->getConnection();
    
    // Check if form_submissions table exists and get column info
    $sql = "DESCRIBE form_submissions";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $columns = array_column($stmt->fetchAll(PDO::FETCH_ASSOC), 'Field');
    
    $dataColumn = 'submission_data';
    if (in_array('form_data', $columns) && !in_array('submission_data', $columns)) {
        $dataColumn = 'form_data';
    }
    
    echo "<h2>Database Info</h2>";
    echo "<p><strong>Data column detected:</strong> $dataColumn</p>";
    
    // Create test submissions
    $testSubmissions = [
        [
            'page_id' => 1,
            'form_name' => 'contact_form',
            'data' => [
                'name' => '<PERSON>',
                'email' => '<EMAIL>',
                'message' => 'This is a test contact form submission.',
                'phone' => '******-0123'
            ]
        ],
        [
            'page_id' => 1,
            'form_name' => 'newsletter_signup',
            'data' => [
                'email' => '<EMAIL>',
                'interests' => 'Technology, Web Development',
                'frequency' => 'weekly'
            ]
        ],
        [
            'page_id' => 1,
            'form_name' => 'feedback_form',
            'data' => [
                'rating' => '5',
                'feedback' => 'Great service! Very satisfied with the experience.',
                'recommend' => 'yes',
                'category' => 'general'
            ]
        ],
        [
            'page_id' => 1,
            'form_name' => 'support_request',
            'data' => [
                'subject' => 'Login Issue',
                'description' => 'Unable to login to my account. Getting error message.',
                'priority' => 'medium',
                'user_id' => '12345'
            ]
        ]
    ];
    
    echo "<h2>Creating Test Submissions</h2>";
    
    $created = 0;
    foreach ($testSubmissions as $submission) {
        try {
            $sql = "INSERT INTO form_submissions (page_id, $dataColumn, ip_address, user_agent, submitted_at) 
                    VALUES (?, ?, ?, ?, NOW())";
            
            $stmt = $db->prepare($sql);
            $result = $stmt->execute([
                $submission['page_id'],
                json_encode($submission['data']),
                '127.0.0.1',
                'Test User Agent - ' . $submission['form_name']
            ]);
            
            if ($result) {
                $id = $db->lastInsertId();
                echo "<p>✅ Created submission ID $id for {$submission['form_name']}</p>";
                $created++;
            } else {
                echo "<p>❌ Failed to create submission for {$submission['form_name']}</p>";
            }
            
        } catch (Exception $e) {
            echo "<p>❌ Error creating {$submission['form_name']}: " . $e->getMessage() . "</p>";
        }
    }
    
    echo "<h2>Summary</h2>";
    echo "<p><strong>Created:</strong> $created test submissions</p>";
    
    // Verify the data
    $sql = "SELECT COUNT(*) as total FROM form_submissions";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $count = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo "<p><strong>Total submissions in database:</strong> {$count['total']}</p>";
    
    if ($count['total'] > 0) {
        echo "<h2>Sample Data Verification</h2>";
        
        $sql = "SELECT id, $dataColumn as form_data, submitted_at FROM form_submissions ORDER BY submitted_at DESC LIMIT 3";
        $stmt = $db->prepare($sql);
        $stmt->execute();
        $samples = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        foreach ($samples as $sample) {
            echo "<h3>Submission ID: {$sample['id']}</h3>";
            echo "<p><strong>Submitted:</strong> {$sample['submitted_at']}</p>";
            echo "<p><strong>Raw Data:</strong></p>";
            echo "<pre>" . htmlspecialchars($sample['form_data']) . "</pre>";
            
            try {
                $parsed = json_decode($sample['form_data'], true);
                if (json_last_error() === JSON_ERROR_NONE) {
                    echo "<p><strong>✅ Parsed Data:</strong></p>";
                    echo "<pre>" . htmlspecialchars(json_encode($parsed, JSON_PRETTY_PRINT)) . "</pre>";
                    
                    // Test preview format
                    if (!empty($parsed) && is_array($parsed)) {
                        $fields = array_keys($parsed);
                        $preview = array_slice($fields, 0, 3);
                        $previewText = implode(', ', array_map(function($field) use ($parsed) {
                            $value = $parsed[$field];
                            $displayValue = strlen($value) > 20 ? substr($value, 0, 20) . '...' : $value;
                            return "$field: $displayValue";
                        }, $preview));
                        echo "<p><strong>Preview:</strong> " . htmlspecialchars($previewText) . "</p>";
                    } else {
                        echo "<p><strong>⚠️ Empty or invalid data structure</strong></p>";
                    }
                } else {
                    echo "<p><strong>❌ Invalid JSON:</strong> " . json_last_error_msg() . "</p>";
                }
            } catch (Exception $e) {
                echo "<p><strong>❌ Parse Error:</strong> " . $e->getMessage() . "</p>";
            }
            
            echo "<hr>";
        }
    }
    
    echo "<h2>Next Steps</h2>";
    echo "<div style='background: #d4edda; color: #155724; padding: 15px; border-radius: 5px;'>";
    echo "<p><strong>✅ Test data created successfully!</strong></p>";
    echo "<p>You can now:</p>";
    echo "<ul>";
    echo "<li>Test the main application Database tab</li>";
    echo "<li>Verify form data displays correctly</li>";
    echo "<li>Test the delete functionality</li>";
    echo "<li>Check the API responses</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<h2>Quick Links</h2>";
    echo "<a href='index.html#database' target='_blank' style='background: #007cba; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>Open Database Tab</a>";
    echo "<a href='test_api_direct.php' target='_blank' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>Test API Direct</a>";
    echo "<a href='test_form_data_viewing_and_deletion.php' target='_blank' style='background: #6f42c1; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>Test Full Functionality</a>";
    
} catch (Exception $e) {
    echo "<h2>❌ Error</h2>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
h1, h2, h3 { color: #333; }
pre { background: #f5f5f5; padding: 10px; border-radius: 5px; overflow-x: auto; }
hr { margin: 20px 0; border: none; border-top: 1px solid #ddd; }
</style>
