# Manual Database Fix Instructions

If the automated scripts aren't working, you can fix the database manually using phpMyAdmin or MySQL command line.

## Option 1: Using phpMyAdmin

1. **Open phpMyAdmin** in your browser (usually `http://localhost/phpmyadmin`)
2. **Select your database** (probably named `fish` or similar)
3. **Click on the SQL tab**
4. **Copy and paste the following SQL commands** and click "Go":

```sql
-- Disable foreign key checks to avoid constraint errors
SET FOREIGN_KEY_CHECKS = 0;

-- Drop tables in correct order (child tables first)
DROP TABLE IF EXISTS share_access_log;
DROP TABLE IF EXISTS form_submissions;
DROP TABLE IF EXISTS page_shares;

-- Create the table with correct structure
CREATE TABLE page_shares (
    id INT AUTO_INCREMENT PRIMARY KEY,
    page_id INT NOT NULL,
    share_token VARCHAR(64) NOT NULL UNIQUE,
    short_code VARCHAR(10) NOT NULL UNIQUE,
    title VARCHAR(255),
    description TEXT,
    password_hash VARCHAR(255),
    expires_at TIMESTAMP NULL,
    max_views INT NULL,
    view_count INT DEFAULT 0,
    allow_download BOOLEAN DEFAULT FALSE,
    show_forms BOOLEAN DEFAULT TRUE,
    show_metadata BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    created_by INT DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_page_id (page_id),
    INDEX idx_share_token (share_token),
    INDEX idx_short_code (short_code),
    INDEX idx_created_at (created_at),
    INDEX idx_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create form_submissions table
CREATE TABLE IF NOT EXISTS form_submissions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    page_id INT NOT NULL,
    form_id INT NULL,
    share_id INT NULL,
    form_data JSON NOT NULL,
    ip_address VARCHAR(45),
    user_agent TEXT,
    referrer TEXT,
    submitted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    processed BOOLEAN DEFAULT FALSE,
    notes TEXT,
    INDEX idx_page_id (page_id),
    INDEX idx_form_id (form_id),
    INDEX idx_share_id (share_id),
    INDEX idx_submitted_at (submitted_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create share_access_log table
CREATE TABLE IF NOT EXISTS share_access_log (
    id INT AUTO_INCREMENT PRIMARY KEY,
    share_id INT NOT NULL,
    ip_address VARCHAR(45),
    user_agent TEXT,
    referrer TEXT,
    access_type ENUM('view', 'form_submission', 'download') DEFAULT 'view',
    accessed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_share_id (share_id),
    INDEX idx_accessed_at (accessed_at),
    INDEX idx_access_type (access_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Re-enable foreign key checks
SET FOREIGN_KEY_CHECKS = 1;

-- Verify the tables were created correctly
DESCRIBE page_shares;
DESCRIBE form_submissions;
DESCRIBE share_access_log;
```

## Option 2: Using MySQL Command Line

1. **Open Command Prompt** or Terminal
2. **Connect to MySQL**:
   ```bash
   mysql -u root -p
   ```
3. **Select your database**:
   ```sql
   USE your_database_name;
   ```
4. **Run the same SQL commands** as shown in Option 1

## Option 3: Quick Fix Script

If you prefer, you can also run this PHP script directly:

1. **Create a file** called `quick_fix.php` in your fish directory
2. **Add this content**:

```php
<?php
require_once 'config/database.php';

try {
    $database = new Database();
    $db = $database->getConnection();
    
    // Drop and recreate page_shares table
    $db->exec("DROP TABLE IF EXISTS page_shares");
    
    $sql = "CREATE TABLE page_shares (
        id INT AUTO_INCREMENT PRIMARY KEY,
        page_id INT NOT NULL,
        share_token VARCHAR(64) NOT NULL UNIQUE,
        short_code VARCHAR(10) NOT NULL UNIQUE,
        title VARCHAR(255),
        description TEXT,
        password_hash VARCHAR(255),
        expires_at TIMESTAMP NULL,
        max_views INT NULL,
        view_count INT DEFAULT 0,
        allow_download BOOLEAN DEFAULT FALSE,
        show_forms BOOLEAN DEFAULT TRUE,
        show_metadata BOOLEAN DEFAULT FALSE,
        is_active BOOLEAN DEFAULT TRUE,
        created_by INT DEFAULT 1,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_page_id (page_id),
        INDEX idx_share_token (share_token),
        INDEX idx_short_code (short_code),
        INDEX idx_created_at (created_at),
        INDEX idx_is_active (is_active)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $db->exec($sql);
    echo "✅ page_shares table created successfully!";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage();
}
?>
```

3. **Run the script** by visiting `http://localhost/fish/quick_fix.php`

## Verification

After running any of these fixes, verify the table structure by running:

```sql
DESCRIBE page_shares;
```

You should see a table with these columns:
- id
- **page_id** ← This is the critical column that was missing
- share_token
- short_code
- title
- description
- password_hash
- expires_at
- max_views
- view_count
- allow_download
- show_forms
- show_metadata
- is_active
- created_by
- created_at
- updated_at

## Test the Fix

After fixing the database:

1. **Test form submissions**: Visit `http://localhost/fish/test_form_submission.php`
2. **Test sharing system**: Visit `http://localhost/fish/test_submit_form.php`
3. **Use main application**: Visit `http://localhost/fish/index.html`

The error "Column not found: page_id" should now be resolved!
