<?php
/**
 * Update Database for Redirect Functionality
 * Add redirect_url column to page_shares table
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once 'config/database.php';

echo "<h1>Update Database for Redirect Functionality</h1>";

try {
    $database = new Database();
    $db = $database->getConnection();
    
    echo "<h2>1. Check Current page_shares Table Structure</h2>";
    
    $sql = "DESCRIBE page_shares";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $columnNames = array_column($columns, 'Field');
    
    echo "<p>Current columns: " . implode(', ', $columnNames) . "</p>";
    
    if (in_array('redirect_url', $columnNames)) {
        echo "<p>✅ redirect_url column already exists</p>";
    } else {
        echo "<p>❌ redirect_url column missing, adding...</p>";
        
        $sql = "ALTER TABLE page_shares ADD COLUMN redirect_url VARCHAR(500) NULL AFTER show_metadata";
        $db->exec($sql);
        
        echo "<p>✅ Added redirect_url column to page_shares table</p>";
    }
    
    echo "<h2>2. Verify Updated Structure</h2>";
    
    $sql = "DESCRIBE page_shares";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $updatedColumns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' cellpadding='5' style='border-collapse: collapse;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    foreach ($updatedColumns as $col) {
        $highlight = $col['Field'] === 'redirect_url' ? 'style="background: #d4edda;"' : '';
        echo "<tr $highlight>";
        echo "<td>{$col['Field']}</td>";
        echo "<td>{$col['Type']}</td>";
        echo "<td>{$col['Null']}</td>";
        echo "<td>{$col['Key']}</td>";
        echo "<td>{$col['Default']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h2>3. Test Redirect URL Storage</h2>";
    
    // Test inserting a share with redirect URL
    $testPageId = 1; // Assuming page ID 1 exists
    $shareToken = bin2hex(random_bytes(16));
    $shortCode = substr(str_shuffle('ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'), 0, 6);
    $redirectUrl = 'https://example.com/thank-you';
    
    $sql = "INSERT INTO page_shares (
                page_id, share_token, short_code, title, description, 
                show_forms, show_metadata, redirect_url, is_active
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
    
    $stmt = $db->prepare($sql);
    $stmt->execute([
        $testPageId,
        $shareToken,
        $shortCode,
        'Test Redirect Share',
        'Testing redirect functionality',
        1, // show_forms
        0, // show_metadata
        $redirectUrl,
        1  // is_active
    ]);
    
    $testShareId = $db->lastInsertId();
    echo "<p>✅ Test share created with ID: $testShareId</p>";
    
    // Verify the redirect URL was stored
    $sql = "SELECT redirect_url FROM page_shares WHERE id = ?";
    $stmt = $db->prepare($sql);
    $stmt->execute([$testShareId]);
    $storedRedirectUrl = $stmt->fetchColumn();
    
    if ($storedRedirectUrl === $redirectUrl) {
        echo "<p>✅ Redirect URL stored correctly: $storedRedirectUrl</p>";
    } else {
        echo "<p>❌ Redirect URL storage failed</p>";
    }
    
    // Clean up test data
    $sql = "DELETE FROM page_shares WHERE id = ?";
    $stmt = $db->prepare($sql);
    $stmt->execute([$testShareId]);
    echo "<p>✅ Test data cleaned up</p>";
    
    echo "<div style='background: #d4edda; color: #155724; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h2>🎉 Database Update Complete!</h2>";
    echo "<p>The page_shares table now supports redirect URLs for form submissions.</p>";
    echo "<ul>";
    echo "<li>✅ redirect_url column added</li>";
    echo "<li>✅ Column accepts URLs up to 500 characters</li>";
    echo "<li>✅ NULL values allowed for optional redirects</li>";
    echo "<li>✅ Storage and retrieval tested successfully</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 20px; border-radius: 8px;'>";
    echo "<h2>❌ Database Update Failed</h2>";
    echo "<p>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p>File: " . $e->getFile() . " Line: " . $e->getLine() . "</p>";
    echo "</div>";
}

echo "<h2>Next Steps</h2>";
echo "<ul>";
echo "<li>Update sharing_manager.php to handle redirect URLs</li>";
echo "<li>Update view.php to use redirect URLs from shares</li>";
echo "<li>Test the enhanced share creation popup</li>";
echo "<li>Verify form submissions redirect correctly</li>";
echo "</ul>";

?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
table { border-collapse: collapse; margin: 10px 0; }
th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
th { background-color: #f2f2f2; }
h1, h2, h3 { color: #333; }
</style>
