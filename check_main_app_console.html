<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Check Main App Console</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .console-output { background: #000; color: #0f0; padding: 15px; border-radius: 5px; font-family: monospace; height: 300px; overflow-y: auto; margin: 10px 0; }
        .error { color: #f00; }
        .warning { color: #ff0; }
        .info { color: #0ff; }
        .btn { background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin: 5px; }
    </style>
</head>
<body>
    <h1>Check Main App Console</h1>
    
    <div style="background: #fff3cd; color: #856404; padding: 15px; border-radius: 5px; margin: 10px 0;">
        <h3>⚠️ Console Monitoring</h3>
        <p>This page will capture console messages from the main application to help debug issues.</p>
    </div>

    <button onclick="openMainApp()" class="btn">Open Main App in New Tab</button>
    <button onclick="testDirectAPI()" class="btn">Test API Directly</button>
    <button onclick="clearConsole()" class="btn">Clear Console</button>

    <h2>Console Output:</h2>
    <div id="console" class="console-output">
        <div class="info">[INFO] Console monitoring started...</div>
    </div>

    <h2>Manual Test Results:</h2>
    <div id="manualResults" style="background: #f8f9fa; padding: 15px; border-radius: 5px;">
        <p>Please perform these manual tests and report results:</p>
        <ol>
            <li><strong>Open Main App:</strong> Click "Open Main App in New Tab" above</li>
            <li><strong>Navigate to Database Tab:</strong> Click on the Database tab</li>
            <li><strong>Check Form Data Display:</strong> 
                <ul>
                    <li>Do you see actual form data or "Invalid data"?</li>
                    <li>Are the previews showing real content like "name: John Doe, email: <EMAIL>"?</li>
                </ul>
            </li>
            <li><strong>Check Delete Buttons:</strong>
                <ul>
                    <li>Do you see trash can icons in the Actions column?</li>
                    <li>Are they clickable?</li>
                </ul>
            </li>
            <li><strong>Test Delete Functionality:</strong>
                <ul>
                    <li>Click a delete button</li>
                    <li>Does a confirmation dialog appear?</li>
                    <li>Does the submission get deleted?</li>
                </ul>
            </li>
        </ol>
        
        <h3>Report Your Results:</h3>
        <div style="margin: 10px 0;">
            <label>Form Data Display: 
                <select id="dataDisplay" onchange="updateResults()">
                    <option value="">Select...</option>
                    <option value="working">✅ Shows actual data</option>
                    <option value="invalid">❌ Shows "Invalid data"</option>
                    <option value="empty">⚠️ Shows "Empty form" or "No data"</option>
                </select>
            </label>
        </div>
        
        <div style="margin: 10px 0;">
            <label>Delete Buttons: 
                <select id="deleteButtons" onchange="updateResults()">
                    <option value="">Select...</option>
                    <option value="visible">✅ Visible and working</option>
                    <option value="missing">❌ Not visible</option>
                    <option value="broken">⚠️ Visible but not working</option>
                </select>
            </label>
        </div>
        
        <div id="testResults" style="margin: 15px 0;"></div>
    </div>

    <script>
        // Capture console messages
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        
        function addToConsole(message, type = 'info') {
            const consoleDiv = document.getElementById('console');
            const timestamp = new Date().toLocaleTimeString();
            const div = document.createElement('div');
            div.className = type;
            div.textContent = `[${timestamp}] [${type.toUpperCase()}] ${message}`;
            consoleDiv.appendChild(div);
            consoleDiv.scrollTop = consoleDiv.scrollHeight;
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            addToConsole(args.join(' '), 'info');
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            addToConsole(args.join(' '), 'error');
        };
        
        console.warn = function(...args) {
            originalWarn.apply(console, args);
            addToConsole(args.join(' '), 'warning');
        };
        
        function openMainApp() {
            addToConsole('Opening main application...', 'info');
            window.open('index.html#database', '_blank');
            addToConsole('Main app opened. Check the new tab and perform manual tests.', 'info');
        }
        
        async function testDirectAPI() {
            addToConsole('Testing API directly...', 'info');
            
            try {
                const formData = new FormData();
                formData.append('action', 'get_submissions');
                
                const response = await fetch('includes/sharing_manager.php', {
                    method: 'POST',
                    body: formData
                });
                
                addToConsole(`API Response Status: ${response.status}`, 'info');
                
                const text = await response.text();
                addToConsole(`Raw Response: ${text.substring(0, 200)}...`, 'info');
                
                try {
                    const data = JSON.parse(text);
                    
                    if (data.success) {
                        addToConsole(`✅ API Success: ${data.total} total submissions, ${data.submissions.length} loaded`, 'info');
                        
                        if (data.submissions.length > 0) {
                            const sample = data.submissions[0];
                            addToConsole(`Sample submission ID: ${sample.id}`, 'info');
                            addToConsole(`Sample form_data: ${sample.form_data}`, 'info');
                            
                            // Test preview formatting
                            try {
                                if (sample.form_data) {
                                    const parsed = JSON.parse(sample.form_data);
                                    if (parsed && typeof parsed === 'object' && Object.keys(parsed).length > 0) {
                                        const fields = Object.keys(parsed).slice(0, 3);
                                        const preview = fields.map(field => `${field}: ${parsed[field]}`).join(', ');
                                        addToConsole(`✅ Preview would show: ${preview}`, 'info');
                                    } else {
                                        addToConsole(`⚠️ Preview would show: Empty form`, 'warning');
                                    }
                                } else {
                                    addToConsole(`⚠️ Preview would show: No data`, 'warning');
                                }
                            } catch (e) {
                                addToConsole(`❌ Preview error: ${e.message}`, 'error');
                            }
                        }
                    } else {
                        addToConsole(`❌ API Error: ${data.message}`, 'error');
                    }
                } catch (parseError) {
                    addToConsole(`❌ JSON Parse Error: ${parseError.message}`, 'error');
                }
            } catch (error) {
                addToConsole(`❌ Network Error: ${error.message}`, 'error');
            }
        }
        
        function clearConsole() {
            document.getElementById('console').innerHTML = '<div class="info">[INFO] Console cleared...</div>';
        }
        
        function updateResults() {
            const dataDisplay = document.getElementById('dataDisplay').value;
            const deleteButtons = document.getElementById('deleteButtons').value;
            const resultsDiv = document.getElementById('testResults');
            
            if (dataDisplay && deleteButtons) {
                let status = 'success';
                let message = '';
                
                if (dataDisplay === 'working' && deleteButtons === 'visible') {
                    message = '🎉 All functionality working correctly!';
                    status = 'success';
                } else if (dataDisplay === 'invalid' || deleteButtons === 'missing') {
                    message = '❌ Issues still present - fixes not working';
                    status = 'error';
                } else {
                    message = '⚠️ Partial functionality - some issues remain';
                    status = 'warning';
                }
                
                const bgColor = status === 'success' ? '#d4edda' : status === 'error' ? '#f8d7da' : '#fff3cd';
                const textColor = status === 'success' ? '#155724' : status === 'error' ? '#721c24' : '#856404';
                
                resultsDiv.innerHTML = `
                    <div style="background: ${bgColor}; color: ${textColor}; padding: 15px; border-radius: 5px;">
                        <h4>Test Results Summary</h4>
                        <p><strong>Form Data Display:</strong> ${dataDisplay}</p>
                        <p><strong>Delete Buttons:</strong> ${deleteButtons}</p>
                        <p><strong>Overall Status:</strong> ${message}</p>
                    </div>
                `;
                
                addToConsole(`Manual test results: Data=${dataDisplay}, Delete=${deleteButtons}`, 'info');
            }
        }
        
        // Auto-test API on load
        window.addEventListener('load', function() {
            setTimeout(testDirectAPI, 1000);
        });
    </script>
</body>
</html>
