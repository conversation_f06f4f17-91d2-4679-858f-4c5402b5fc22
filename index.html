<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="theme-color" content="#007cba">
    <title>Webpage Manager - Import & Analyze Forms</title>
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/mobile-enhancements.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <header class="header">
            <h1><i class="fas fa-code"></i> Webpage Manager</h1>
            <p>Import, analyze, and manage webpages with form detection</p>
        </header>

        <nav class="nav-tabs">
            <button class="tab-btn active" data-tab="import">
                <i class="fas fa-upload"></i> Import Pages
            </button>
            <button class="tab-btn" data-tab="manage">
                <i class="fas fa-list"></i> Manage Pages
            </button>
            <button class="tab-btn" data-tab="forms">
                <i class="fas fa-wpforms"></i> Forms
            </button>
            <button class="tab-btn" data-tab="database">
                <i class="fas fa-database"></i> Database
            </button>
            <button class="tab-btn" data-tab="sharing">
                <i class="fas fa-share-alt"></i> Sharing
            </button>
        </nav>

        <!-- Import Tab -->
        <div class="tab-content active" id="import-tab">
            <div class="upload-section">
                <h2>Import HTML Pages & Assets</h2>

                <!-- Upload Options -->
                <div class="upload-options">
                    <div class="upload-mode">
                        <label class="radio-label">
                            <input type="radio" name="upload-mode" value="individual" checked>
                            <span>Individual Files</span>
                            <small>Upload HTML pages with their associated CSS, JS, and image files</small>
                        </label>
                        <label class="radio-label">
                            <input type="radio" name="upload-mode" value="zip">
                            <span>ZIP Archive</span>
                            <small>Upload complete websites as ZIP files (maintains folder structure)</small>
                        </label>
                    </div>

                    <div class="project-selection">
                        <label for="project-select">Project:</label>
                        <select id="project-select">
                            <option value="1">Default Project</option>
                        </select>
                        <button type="button" class="btn btn-sm" onclick="app.showCreateProjectModal()">
                            <i class="fas fa-plus"></i> New Project
                        </button>
                    </div>
                </div>

                <!-- Upload Area -->
                <div class="upload-area" id="upload-area">
                    <div class="upload-content">
                        <i class="fas fa-cloud-upload-alt"></i>
                        <h3 id="upload-title">Drag & Drop Files Here</h3>
                        <p id="upload-subtitle">Or click to select files</p>
                        <p class="file-types" id="file-types">Supported: HTML, CSS, JS, Images, Fonts, ZIP</p>
                    </div>
                    <input type="file" id="file-input" multiple accept=".html,.htm,.css,.js,.png,.jpg,.jpeg,.gif,.svg,.woff,.woff2,.ttf,.otf,.zip">
                </div>

                <!-- Upload Progress -->
                <div class="upload-progress" id="upload-progress" style="display: none;">
                    <div class="progress-bar">
                        <div class="progress-fill" id="progress-fill"></div>
                    </div>
                    <div class="progress-text" id="progress-text">Uploading...</div>
                </div>

                <!-- Upload Results -->
                <div class="upload-results" id="upload-results">
                    <!-- Upload results will appear here -->
                </div>
            </div>
        </div>

        <!-- Manage Tab -->
        <div class="tab-content" id="manage-tab">
            <div class="manage-section">
                <h2>Imported Pages</h2>

                <div class="manage-controls">
                    <div class="search-controls">
                        <input type="text" id="search-pages" placeholder="Search pages..." class="search-input">
                        <select id="status-filter" class="status-filter">
                            <option value="active">Active Pages</option>
                            <option value="archived">Archived Pages</option>
                            <option value="all">All Pages</option>
                        </select>
                        <select id="project-filter" class="project-filter">
                            <option value="">All Projects</option>
                        </select>
                    </div>

                    <div class="action-controls">
                        <button id="refresh-pages" class="btn btn-secondary">
                            <i class="fas fa-sync-alt"></i> Refresh
                        </button>
                        <button id="select-all-pages" class="btn btn-secondary">
                            <i class="fas fa-check-square"></i> Select All
                        </button>
                        <button id="bulk-actions-btn" class="btn btn-primary" style="display: none;">
                            <i class="fas fa-cog"></i> Bulk Actions
                        </button>
                    </div>
                </div>

                <!-- Bulk Actions Panel -->
                <div class="bulk-actions-panel" id="bulk-actions-panel" style="display: none;">
                    <div class="bulk-actions-content">
                        <span class="selected-count">0 pages selected</span>
                        <div class="bulk-buttons">
                            <button class="btn btn-warning" onclick="app.bulkArchive()">
                                <i class="fas fa-archive"></i> Archive
                            </button>
                            <button class="btn btn-success" onclick="app.bulkRestore()">
                                <i class="fas fa-undo"></i> Restore
                            </button>
                            <button class="btn btn-danger" onclick="app.bulkDelete(false)">
                                <i class="fas fa-trash"></i> Delete (Keep Files)
                            </button>
                            <button class="btn btn-danger" onclick="app.bulkDelete(true)">
                                <i class="fas fa-trash-alt"></i> Delete (Remove Files)
                            </button>
                            <button class="btn btn-secondary" onclick="app.clearSelection()">
                                <i class="fas fa-times"></i> Clear Selection
                            </button>
                        </div>
                    </div>
                </div>

                <div class="pages-grid" id="pages-grid">
                    <!-- Pages will be loaded here -->
                </div>

                <!-- Page Statistics -->
                <div class="page-stats" id="page-stats">
                    <div class="stat-item">
                        <span class="stat-label">Total Pages:</span>
                        <span class="stat-value" id="total-pages">0</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Active:</span>
                        <span class="stat-value" id="active-pages">0</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Archived:</span>
                        <span class="stat-value" id="archived-pages">0</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Total Size:</span>
                        <span class="stat-value" id="total-size">0 MB</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Forms Tab -->
        <div class="tab-content" id="forms-tab">
            <div class="forms-section">
                <h2>Form Management</h2>

                <div class="forms-controls">
                    <div class="page-selector">
                        <label for="page-select-forms">Select Page:</label>
                        <select id="page-select-forms" onchange="app.loadPageForms()">
                            <option value="">Choose a page...</option>
                        </select>
                        <button id="refresh-forms" class="btn btn-secondary" onclick="app.loadPageForms()">
                            <i class="fas fa-sync-alt"></i> Refresh
                        </button>
                    </div>

                    <div class="form-actions">
                        <button id="save-all-forms" class="btn btn-primary" onclick="app.saveAllForms()">
                            <i class="fas fa-save"></i> Save All Changes
                        </button>
                        <button id="reset-forms" class="btn btn-secondary" onclick="app.resetForms()">
                            <i class="fas fa-undo"></i> Reset Changes
                        </button>
                    </div>
                </div>

                <div class="forms-container" id="forms-container">
                    <div class="no-forms-message">
                        <i class="fas fa-wpforms"></i>
                        <h3>No Forms Found</h3>
                        <p>Select a page to view and edit its forms, or import pages with forms to get started.</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Database Tab -->
        <div class="tab-content" id="database-tab">
            <div class="database-section">
                <div class="database-nav">
                    <button class="db-nav-btn active" data-section="structure">
                        <i class="fas fa-table"></i> Database Structure
                    </button>
                    <button class="db-nav-btn" data-section="submissions">
                        <i class="fas fa-paper-plane"></i> Form Submissions
                    </button>
                    <button class="db-nav-btn" data-section="analytics">
                        <i class="fas fa-chart-bar"></i> Analytics
                    </button>
                </div>

                <!-- Database Structure Section -->
                <div class="db-section active" id="db-structure-section">
                    <h2>Database Structure</h2>
                    <div class="db-actions">
                        <button id="generate-db" class="btn btn-primary">
                            <i class="fas fa-magic"></i> Generate Database Structure
                        </button>
                        <button id="export-sql" class="btn btn-secondary">
                            <i class="fas fa-download"></i> Export SQL
                        </button>
                    </div>
                    <div class="db-tables" id="db-tables">
                        <!-- Database tables will be shown here -->
                    </div>
                </div>

                <!-- Form Submissions Section -->
                <div class="db-section" id="db-submissions-section">
                    <h2>Form Submissions</h2>
                    <div class="submissions-controls">
                        <div class="submissions-filters">
                            <select id="submissions-page-filter">
                                <option value="">All Pages</option>
                            </select>
                            <select id="submissions-form-filter">
                                <option value="">All Forms</option>
                            </select>
                            <input type="date" id="submissions-date-from" placeholder="From Date">
                            <input type="date" id="submissions-date-to" placeholder="To Date">
                            <button id="filter-submissions" class="btn btn-secondary">
                                <i class="fas fa-filter"></i> Filter
                            </button>
                        </div>
                        <div class="submissions-actions">
                            <button id="export-submissions" class="btn btn-success">
                                <i class="fas fa-download"></i> Export CSV
                            </button>
                            <button id="refresh-submissions" class="btn btn-secondary">
                                <i class="fas fa-sync-alt"></i> Refresh
                            </button>
                        </div>
                    </div>
                    <div class="submissions-container" id="submissions-container">
                        <div class="no-submissions-message">
                            <i class="fas fa-paper-plane"></i>
                            <h3>No Form Submissions</h3>
                            <p>Form submissions from shared pages will appear here.</p>
                        </div>
                    </div>
                </div>

                <!-- Analytics Section -->
                <div class="db-section" id="db-analytics-section">
                    <h2>Analytics & Statistics</h2>
                    <div class="analytics-grid">
                        <div class="analytics-card">
                            <h3>Total Submissions</h3>
                            <div class="analytics-value" id="total-submissions">0</div>
                        </div>
                        <div class="analytics-card">
                            <h3>This Month</h3>
                            <div class="analytics-value" id="monthly-submissions">0</div>
                        </div>
                        <div class="analytics-card">
                            <h3>Active Shares</h3>
                            <div class="analytics-value" id="active-shares">0</div>
                        </div>
                        <div class="analytics-card">
                            <h3>Total Views</h3>
                            <div class="analytics-value" id="total-views">0</div>
                        </div>
                    </div>
                    <div class="analytics-charts">
                        <div class="chart-container">
                            <h4>Submissions Over Time</h4>
                            <div id="submissions-chart" class="chart-placeholder">
                                Chart will be displayed here
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sharing Tab -->
        <div class="tab-content" id="sharing-tab">
            <div class="sharing-section">
                <h2>Page Sharing & URL Shortener</h2>

                <div class="sharing-actions">
                    <button id="create-share" class="btn btn-primary">
                        <i class="fas fa-share-alt"></i> Create New Share
                    </button>
                    <button id="create-short-url" class="btn btn-secondary">
                        <i class="fas fa-link"></i> Shorten URL
                    </button>
                    <button id="refresh-shares" class="btn btn-secondary">
                        <i class="fas fa-refresh"></i> Refresh
                    </button>
                </div>

                <div class="shares-container">
                    <h3>Active Shares</h3>
                    <div class="shares-grid" id="shares-grid">
                        <!-- Shares will be loaded here -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal for Page Details -->
    <div class="modal" id="page-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modal-title">Page Details</h3>
                <button class="modal-close" id="modal-close">&times;</button>
            </div>
            <div class="modal-body" id="modal-body">
                <!-- Page details will be loaded here -->
            </div>
        </div>
    </div>

    <!-- Enhanced Modal for Creating Shares -->
    <div class="modal" id="share-modal">
        <div class="modal-content enhanced-share-modal">
            <div class="modal-header">
                <div class="modal-title-section">
                    <i class="fas fa-share-alt modal-icon"></i>
                    <h3>Create New Share</h3>
                </div>
                <button class="modal-close" onclick="app.closeShareModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="share-form">
                    <!-- Page Selection Section -->
                    <div class="form-section">
                        <h4><i class="fas fa-file-alt"></i> Page Selection</h4>
                        <div class="form-group">
                            <label for="share-page-select">Select Page to Share:</label>
                            <select id="share-page-select" required>
                                <option value="">Choose a page to share...</option>
                            </select>
                            <small class="form-help">Select the HTML page you want to share with others</small>
                        </div>
                    </div>

                    <!-- Basic Information Section -->
                    <div class="form-section">
                        <h4><i class="fas fa-info-circle"></i> Share Information</h4>
                        <div class="form-group">
                            <label for="share-title">Share Title:</label>
                            <input type="text" id="share-title" placeholder="Enter a custom title for the shared page">
                            <small class="form-help">This title will be displayed to visitors</small>
                        </div>

                        <div class="form-group">
                            <label for="share-description">Description:</label>
                            <textarea id="share-description" rows="3" placeholder="Optional description for the shared page"></textarea>
                            <small class="form-help">Provide context about what visitors can expect</small>
                        </div>
                    </div>

                    <!-- Form Behavior Section -->
                    <div class="form-section">
                        <h4><i class="fas fa-cogs"></i> Form Behavior</h4>
                        <div class="form-group">
                            <label for="share-redirect-url">Redirect URL After Form Submission:</label>
                            <input type="text" id="share-redirect-url" placeholder="https://example.com/thank-you">
                            <small class="form-help">Optional: Redirect visitors to this URL after they submit a form</small>
                        </div>

                        <div class="form-group checkbox-group">
                            <label class="checkbox-label enhanced-checkbox">
                                <input type="checkbox" id="share-show-forms" checked>
                                <span class="checkmark"></span>
                                <span class="checkbox-text">
                                    <strong>Enable Forms</strong>
                                    <small>Allow visitors to submit forms on the shared page</small>
                                </span>
                            </label>
                        </div>
                    </div>

                    <!-- Security & Access Section -->
                    <div class="form-section">
                        <h4><i class="fas fa-shield-alt"></i> Security & Access</h4>
                        <div class="form-group">
                            <label for="share-password">Password Protection:</label>
                            <input type="password" id="share-password" placeholder="Leave empty for no password protection">
                            <small class="form-help">Optional: Require a password to access the shared page</small>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="share-expires">Expires At:</label>
                                <input type="datetime-local" id="share-expires">
                                <small class="form-help">Optional expiration date</small>
                            </div>
                            <div class="form-group">
                                <label for="share-max-views">Max Views:</label>
                                <input type="number" id="share-max-views" min="1" placeholder="Unlimited">
                                <small class="form-help">Limit number of views</small>
                            </div>
                        </div>
                    </div>

                    <!-- Advanced Options Section -->
                    <div class="form-section">
                        <h4><i class="fas fa-sliders-h"></i> Advanced Options</h4>
                        <div class="checkbox-group">
                            <label class="checkbox-label enhanced-checkbox">
                                <input type="checkbox" id="share-allow-download">
                                <span class="checkmark"></span>
                                <span class="checkbox-text">
                                    <strong>Allow Download</strong>
                                    <small>Let visitors download the page files</small>
                                </span>
                            </label>

                            <label class="checkbox-label enhanced-checkbox">
                                <input type="checkbox" id="share-show-metadata">
                                <span class="checkmark"></span>
                                <span class="checkbox-text">
                                    <strong>Show Metadata</strong>
                                    <small>Display share information and view count</small>
                                </span>
                            </label>
                        </div>
                    </div>

                    <div class="form-actions enhanced-actions">
                        <button type="submit" class="btn btn-primary enhanced-primary">
                            <i class="fas fa-share-alt"></i>
                            <span>Create Share</span>
                        </button>
                        <button type="button" class="btn btn-secondary enhanced-secondary" onclick="app.closeShareModal()">
                            <i class="fas fa-times"></i>
                            <span>Cancel</span>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Modal for URL Shortener -->
    <div class="modal" id="url-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>URL Shortener</h3>
                <button class="modal-close" onclick="app.closeUrlModal()">&times;</button>
            </div>
            <div class="modal-body">
                <form id="url-form">
                    <div class="form-group">
                        <label for="original-url">URL to Shorten:</label>
                        <input type="url" id="original-url" required placeholder="https://example.com/long-url">
                    </div>

                    <div class="form-group">
                        <label for="url-title">Title (Optional):</label>
                        <input type="text" id="url-title" placeholder="Custom title">
                    </div>

                    <div class="form-group">
                        <label for="url-description">Description (Optional):</label>
                        <textarea id="url-description" rows="2" placeholder="Optional description"></textarea>
                    </div>

                    <div class="form-group">
                        <label for="url-expires">Expires At (Optional):</label>
                        <input type="datetime-local" id="url-expires">
                    </div>

                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-link"></i> Create Short URL
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="app.closeUrlModal()">Cancel</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Modal for Creating Projects -->
    <div class="modal" id="project-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Create New Project</h3>
                <button class="modal-close" onclick="app.closeProjectModal()">&times;</button>
            </div>
            <div class="modal-body">
                <form id="project-form">
                    <div class="form-group">
                        <label for="project-name">Project Name:</label>
                        <input type="text" id="project-name" required placeholder="Enter project name">
                    </div>

                    <div class="form-group">
                        <label for="project-description">Description:</label>
                        <textarea id="project-description" rows="3" placeholder="Optional project description"></textarea>
                    </div>

                    <div class="form-group">
                        <label for="project-color">Color:</label>
                        <input type="color" id="project-color" value="#667eea">
                    </div>

                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Create Project
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="app.closeProjectModal()">Cancel</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Modal for Delete Confirmation -->
    <div class="modal" id="delete-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="delete-modal-title">Confirm Deletion</h3>
                <button class="modal-close" onclick="app.closeDeleteModal()">&times;</button>
            </div>
            <div class="modal-body">
                <div class="delete-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    <p id="delete-modal-message">Are you sure you want to delete this page?</p>
                </div>

                <div class="delete-options">
                    <label class="checkbox-label">
                        <input type="checkbox" id="delete-files-checkbox" checked>
                        <span>Also delete associated files (CSS, JS, images)</span>
                        <small>Unchecking this will keep files on disk but remove from database</small>
                    </label>
                </div>

                <div class="delete-details" id="delete-details">
                    <!-- Details about what will be deleted -->
                </div>

                <div class="form-actions">
                    <button type="button" class="btn btn-danger" id="confirm-delete-btn">
                        <i class="fas fa-trash"></i> Delete
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="app.closeDeleteModal()">Cancel</button>
                </div>
            </div>
        </div>
    </div>

    <script src="assets/js/app.js"></script>
    <script src="assets/js/mobile-enhancements.js"></script>
</body>
</html>
