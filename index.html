<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Webpage Manager - Import & Analyze Forms</title>
    <link rel="stylesheet" href="assets/css/style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <header class="header">
            <h1><i class="fas fa-code"></i> Webpage Manager</h1>
            <p>Import, analyze, and manage webpages with form detection</p>
        </header>

        <nav class="nav-tabs">
            <button class="tab-btn active" data-tab="import">
                <i class="fas fa-upload"></i> Import Pages
            </button>
            <button class="tab-btn" data-tab="manage">
                <i class="fas fa-list"></i> Manage Pages
            </button>
            <button class="tab-btn" data-tab="database">
                <i class="fas fa-database"></i> Database
            </button>
            <button class="tab-btn" data-tab="sharing">
                <i class="fas fa-share-alt"></i> Sharing
            </button>
        </nav>

        <!-- Import Tab -->
        <div class="tab-content active" id="import-tab">
            <div class="upload-section">
                <h2>Import Webpage Files</h2>
                <div class="upload-area" id="upload-area">
                    <div class="upload-content">
                        <i class="fas fa-cloud-upload-alt"></i>
                        <h3>Drag & Drop Files Here</h3>
                        <p>Or click to select files</p>
                        <p class="file-types">Supported: HTML, CSS, JS, Images, Fonts</p>
                    </div>
                    <input type="file" id="file-input" multiple accept=".html,.htm,.css,.js,.png,.jpg,.jpeg,.gif,.svg,.woff,.woff2,.ttf,.otf">
                </div>
                
                <div class="upload-progress" id="upload-progress" style="display: none;">
                    <div class="progress-bar">
                        <div class="progress-fill" id="progress-fill"></div>
                    </div>
                    <div class="progress-text" id="progress-text">Uploading...</div>
                </div>

                <div class="upload-results" id="upload-results"></div>
            </div>
        </div>

        <!-- Manage Tab -->
        <div class="tab-content" id="manage-tab">
            <div class="manage-section">
                <h2>Imported Pages</h2>
                <div class="search-bar">
                    <input type="text" id="search-pages" placeholder="Search pages...">
                    <button id="refresh-pages"><i class="fas fa-refresh"></i></button>
                </div>
                <div class="pages-grid" id="pages-grid">
                    <!-- Pages will be loaded here -->
                </div>
            </div>
        </div>

        <!-- Database Tab -->
        <div class="tab-content" id="database-tab">
            <div class="database-section">
                <h2>Database Structure</h2>
                <div class="db-actions">
                    <button id="generate-db" class="btn btn-primary">
                        <i class="fas fa-magic"></i> Generate Database Structure
                    </button>
                    <button id="export-sql" class="btn btn-secondary">
                        <i class="fas fa-download"></i> Export SQL
                    </button>
                </div>
                <div class="db-tables" id="db-tables">
                    <!-- Database tables will be shown here -->
                </div>
            </div>
        </div>

        <!-- Sharing Tab -->
        <div class="tab-content" id="sharing-tab">
            <div class="sharing-section">
                <h2>Page Sharing & URL Shortener</h2>

                <div class="sharing-actions">
                    <button id="create-share" class="btn btn-primary">
                        <i class="fas fa-share-alt"></i> Create New Share
                    </button>
                    <button id="create-short-url" class="btn btn-secondary">
                        <i class="fas fa-link"></i> Shorten URL
                    </button>
                    <button id="refresh-shares" class="btn btn-secondary">
                        <i class="fas fa-refresh"></i> Refresh
                    </button>
                </div>

                <div class="shares-container">
                    <h3>Active Shares</h3>
                    <div class="shares-grid" id="shares-grid">
                        <!-- Shares will be loaded here -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal for Page Details -->
    <div class="modal" id="page-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modal-title">Page Details</h3>
                <button class="modal-close" id="modal-close">&times;</button>
            </div>
            <div class="modal-body" id="modal-body">
                <!-- Page details will be loaded here -->
            </div>
        </div>
    </div>

    <!-- Modal for Creating Shares -->
    <div class="modal" id="share-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Share Page</h3>
                <button class="modal-close" onclick="app.closeShareModal()">&times;</button>
            </div>
            <div class="modal-body">
                <form id="share-form">
                    <div class="form-group">
                        <label for="share-page-select">Select Page:</label>
                        <select id="share-page-select" required>
                            <option value="">Choose a page to share...</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="share-title">Share Title:</label>
                        <input type="text" id="share-title" placeholder="Custom title for the shared page">
                    </div>

                    <div class="form-group">
                        <label for="share-description">Description:</label>
                        <textarea id="share-description" rows="3" placeholder="Optional description"></textarea>
                    </div>

                    <div class="form-group">
                        <label for="share-password">Password Protection (Optional):</label>
                        <input type="password" id="share-password" placeholder="Leave empty for no password">
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="share-expires">Expires At (Optional):</label>
                            <input type="datetime-local" id="share-expires">
                        </div>
                        <div class="form-group">
                            <label for="share-max-views">Max Views (Optional):</label>
                            <input type="number" id="share-max-views" min="1" placeholder="Unlimited">
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="checkbox-label">
                            <input type="checkbox" id="share-show-forms" checked> Show Forms
                        </label>
                        <label class="checkbox-label">
                            <input type="checkbox" id="share-allow-download"> Allow Download
                        </label>
                        <label class="checkbox-label">
                            <input type="checkbox" id="share-show-metadata"> Show Metadata
                        </label>
                    </div>

                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-share-alt"></i> Create Share
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="app.closeShareModal()">Cancel</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Modal for URL Shortener -->
    <div class="modal" id="url-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>URL Shortener</h3>
                <button class="modal-close" onclick="app.closeUrlModal()">&times;</button>
            </div>
            <div class="modal-body">
                <form id="url-form">
                    <div class="form-group">
                        <label for="original-url">URL to Shorten:</label>
                        <input type="url" id="original-url" required placeholder="https://example.com/long-url">
                    </div>

                    <div class="form-group">
                        <label for="url-title">Title (Optional):</label>
                        <input type="text" id="url-title" placeholder="Custom title">
                    </div>

                    <div class="form-group">
                        <label for="url-description">Description (Optional):</label>
                        <textarea id="url-description" rows="2" placeholder="Optional description"></textarea>
                    </div>

                    <div class="form-group">
                        <label for="url-expires">Expires At (Optional):</label>
                        <input type="datetime-local" id="url-expires">
                    </div>

                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-link"></i> Create Short URL
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="app.closeUrlModal()">Cancel</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="assets/js/app.js"></script>
</body>
</html>
