<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Webpage Manager - Import & Analyze Forms</title>
    <link rel="stylesheet" href="assets/css/style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <header class="header">
            <h1><i class="fas fa-code"></i> Webpage Manager</h1>
            <p>Import, analyze, and manage webpages with form detection</p>
        </header>

        <nav class="nav-tabs">
            <button class="tab-btn active" data-tab="import">
                <i class="fas fa-upload"></i> Import Pages
            </button>
            <button class="tab-btn" data-tab="manage">
                <i class="fas fa-list"></i> Manage Pages
            </button>
            <button class="tab-btn" data-tab="database">
                <i class="fas fa-database"></i> Database
            </button>
            <button class="tab-btn" data-tab="sharing">
                <i class="fas fa-share-alt"></i> Sharing
            </button>
        </nav>

        <!-- Import Tab -->
        <div class="tab-content active" id="import-tab">
            <div class="upload-section">
                <h2>Import HTML Pages & Assets</h2>

                <!-- Upload Options -->
                <div class="upload-options">
                    <div class="upload-mode">
                        <label class="radio-label">
                            <input type="radio" name="upload-mode" value="individual" checked>
                            <span>Individual Files</span>
                            <small>Upload HTML pages with their associated CSS, JS, and image files</small>
                        </label>
                        <label class="radio-label">
                            <input type="radio" name="upload-mode" value="zip">
                            <span>ZIP Archive</span>
                            <small>Upload complete websites as ZIP files (maintains folder structure)</small>
                        </label>
                    </div>

                    <div class="project-selection">
                        <label for="project-select">Project:</label>
                        <select id="project-select">
                            <option value="1">Default Project</option>
                        </select>
                        <button type="button" class="btn btn-sm" onclick="app.showCreateProjectModal()">
                            <i class="fas fa-plus"></i> New Project
                        </button>
                    </div>
                </div>

                <!-- Upload Area -->
                <div class="upload-area" id="upload-area">
                    <div class="upload-content">
                        <i class="fas fa-cloud-upload-alt"></i>
                        <h3 id="upload-title">Drag & Drop Files Here</h3>
                        <p id="upload-subtitle">Or click to select files</p>
                        <p class="file-types" id="file-types">Supported: HTML, CSS, JS, Images, Fonts, ZIP</p>
                    </div>
                    <input type="file" id="file-input" multiple accept=".html,.htm,.css,.js,.png,.jpg,.jpeg,.gif,.svg,.woff,.woff2,.ttf,.otf,.zip">
                </div>

                <!-- Upload Progress -->
                <div class="upload-progress" id="upload-progress" style="display: none;">
                    <div class="progress-bar">
                        <div class="progress-fill" id="progress-fill"></div>
                    </div>
                    <div class="progress-text" id="progress-text">Uploading...</div>
                </div>

                <!-- Upload Results -->
                <div class="upload-results" id="upload-results">
                    <!-- Upload results will appear here -->
                </div>
            </div>
        </div>

        <!-- Manage Tab -->
        <div class="tab-content" id="manage-tab">
            <div class="manage-section">
                <h2>Imported Pages</h2>

                <div class="manage-controls">
                    <div class="search-controls">
                        <input type="text" id="search-pages" placeholder="Search pages..." class="search-input">
                        <select id="status-filter" class="status-filter">
                            <option value="active">Active Pages</option>
                            <option value="archived">Archived Pages</option>
                            <option value="all">All Pages</option>
                        </select>
                        <select id="project-filter" class="project-filter">
                            <option value="">All Projects</option>
                        </select>
                    </div>

                    <div class="action-controls">
                        <button id="refresh-pages" class="btn btn-secondary">
                            <i class="fas fa-sync-alt"></i> Refresh
                        </button>
                        <button id="select-all-pages" class="btn btn-secondary">
                            <i class="fas fa-check-square"></i> Select All
                        </button>
                        <button id="bulk-actions-btn" class="btn btn-primary" style="display: none;">
                            <i class="fas fa-cog"></i> Bulk Actions
                        </button>
                    </div>
                </div>

                <!-- Bulk Actions Panel -->
                <div class="bulk-actions-panel" id="bulk-actions-panel" style="display: none;">
                    <div class="bulk-actions-content">
                        <span class="selected-count">0 pages selected</span>
                        <div class="bulk-buttons">
                            <button class="btn btn-warning" onclick="app.bulkArchive()">
                                <i class="fas fa-archive"></i> Archive
                            </button>
                            <button class="btn btn-success" onclick="app.bulkRestore()">
                                <i class="fas fa-undo"></i> Restore
                            </button>
                            <button class="btn btn-danger" onclick="app.bulkDelete(false)">
                                <i class="fas fa-trash"></i> Delete (Keep Files)
                            </button>
                            <button class="btn btn-danger" onclick="app.bulkDelete(true)">
                                <i class="fas fa-trash-alt"></i> Delete (Remove Files)
                            </button>
                            <button class="btn btn-secondary" onclick="app.clearSelection()">
                                <i class="fas fa-times"></i> Clear Selection
                            </button>
                        </div>
                    </div>
                </div>

                <div class="pages-grid" id="pages-grid">
                    <!-- Pages will be loaded here -->
                </div>

                <!-- Page Statistics -->
                <div class="page-stats" id="page-stats">
                    <div class="stat-item">
                        <span class="stat-label">Total Pages:</span>
                        <span class="stat-value" id="total-pages">0</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Active:</span>
                        <span class="stat-value" id="active-pages">0</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Archived:</span>
                        <span class="stat-value" id="archived-pages">0</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Total Size:</span>
                        <span class="stat-value" id="total-size">0 MB</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Database Tab -->
        <div class="tab-content" id="database-tab">
            <div class="database-section">
                <h2>Database Structure</h2>
                <div class="db-actions">
                    <button id="generate-db" class="btn btn-primary">
                        <i class="fas fa-magic"></i> Generate Database Structure
                    </button>
                    <button id="export-sql" class="btn btn-secondary">
                        <i class="fas fa-download"></i> Export SQL
                    </button>
                </div>
                <div class="db-tables" id="db-tables">
                    <!-- Database tables will be shown here -->
                </div>
            </div>
        </div>

        <!-- Sharing Tab -->
        <div class="tab-content" id="sharing-tab">
            <div class="sharing-section">
                <h2>Page Sharing & URL Shortener</h2>

                <div class="sharing-actions">
                    <button id="create-share" class="btn btn-primary">
                        <i class="fas fa-share-alt"></i> Create New Share
                    </button>
                    <button id="create-short-url" class="btn btn-secondary">
                        <i class="fas fa-link"></i> Shorten URL
                    </button>
                    <button id="refresh-shares" class="btn btn-secondary">
                        <i class="fas fa-refresh"></i> Refresh
                    </button>
                </div>

                <div class="shares-container">
                    <h3>Active Shares</h3>
                    <div class="shares-grid" id="shares-grid">
                        <!-- Shares will be loaded here -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal for Page Details -->
    <div class="modal" id="page-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modal-title">Page Details</h3>
                <button class="modal-close" id="modal-close">&times;</button>
            </div>
            <div class="modal-body" id="modal-body">
                <!-- Page details will be loaded here -->
            </div>
        </div>
    </div>

    <!-- Modal for Creating Shares -->
    <div class="modal" id="share-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Share Page</h3>
                <button class="modal-close" onclick="app.closeShareModal()">&times;</button>
            </div>
            <div class="modal-body">
                <form id="share-form">
                    <div class="form-group">
                        <label for="share-page-select">Select Page:</label>
                        <select id="share-page-select" required>
                            <option value="">Choose a page to share...</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="share-title">Share Title:</label>
                        <input type="text" id="share-title" placeholder="Custom title for the shared page">
                    </div>

                    <div class="form-group">
                        <label for="share-description">Description:</label>
                        <textarea id="share-description" rows="3" placeholder="Optional description"></textarea>
                    </div>

                    <div class="form-group">
                        <label for="share-password">Password Protection (Optional):</label>
                        <input type="password" id="share-password" placeholder="Leave empty for no password">
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="share-expires">Expires At (Optional):</label>
                            <input type="datetime-local" id="share-expires">
                        </div>
                        <div class="form-group">
                            <label for="share-max-views">Max Views (Optional):</label>
                            <input type="number" id="share-max-views" min="1" placeholder="Unlimited">
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="checkbox-label">
                            <input type="checkbox" id="share-show-forms" checked> Show Forms
                        </label>
                        <label class="checkbox-label">
                            <input type="checkbox" id="share-allow-download"> Allow Download
                        </label>
                        <label class="checkbox-label">
                            <input type="checkbox" id="share-show-metadata"> Show Metadata
                        </label>
                    </div>

                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-share-alt"></i> Create Share
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="app.closeShareModal()">Cancel</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Modal for URL Shortener -->
    <div class="modal" id="url-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>URL Shortener</h3>
                <button class="modal-close" onclick="app.closeUrlModal()">&times;</button>
            </div>
            <div class="modal-body">
                <form id="url-form">
                    <div class="form-group">
                        <label for="original-url">URL to Shorten:</label>
                        <input type="url" id="original-url" required placeholder="https://example.com/long-url">
                    </div>

                    <div class="form-group">
                        <label for="url-title">Title (Optional):</label>
                        <input type="text" id="url-title" placeholder="Custom title">
                    </div>

                    <div class="form-group">
                        <label for="url-description">Description (Optional):</label>
                        <textarea id="url-description" rows="2" placeholder="Optional description"></textarea>
                    </div>

                    <div class="form-group">
                        <label for="url-expires">Expires At (Optional):</label>
                        <input type="datetime-local" id="url-expires">
                    </div>

                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-link"></i> Create Short URL
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="app.closeUrlModal()">Cancel</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Modal for Creating Projects -->
    <div class="modal" id="project-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Create New Project</h3>
                <button class="modal-close" onclick="app.closeProjectModal()">&times;</button>
            </div>
            <div class="modal-body">
                <form id="project-form">
                    <div class="form-group">
                        <label for="project-name">Project Name:</label>
                        <input type="text" id="project-name" required placeholder="Enter project name">
                    </div>

                    <div class="form-group">
                        <label for="project-description">Description:</label>
                        <textarea id="project-description" rows="3" placeholder="Optional project description"></textarea>
                    </div>

                    <div class="form-group">
                        <label for="project-color">Color:</label>
                        <input type="color" id="project-color" value="#667eea">
                    </div>

                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Create Project
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="app.closeProjectModal()">Cancel</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Modal for Delete Confirmation -->
    <div class="modal" id="delete-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="delete-modal-title">Confirm Deletion</h3>
                <button class="modal-close" onclick="app.closeDeleteModal()">&times;</button>
            </div>
            <div class="modal-body">
                <div class="delete-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    <p id="delete-modal-message">Are you sure you want to delete this page?</p>
                </div>

                <div class="delete-options">
                    <label class="checkbox-label">
                        <input type="checkbox" id="delete-files-checkbox" checked>
                        <span>Also delete associated files (CSS, JS, images)</span>
                        <small>Unchecking this will keep files on disk but remove from database</small>
                    </label>
                </div>

                <div class="delete-details" id="delete-details">
                    <!-- Details about what will be deleted -->
                </div>

                <div class="form-actions">
                    <button type="button" class="btn btn-danger" id="confirm-delete-btn">
                        <i class="fas fa-trash"></i> Delete
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="app.closeDeleteModal()">Cancel</button>
                </div>
            </div>
        </div>
    </div>

    <script src="assets/js/app.js"></script>
</body>
</html>
