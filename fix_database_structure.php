<?php
/**
 * Fix Database Structure
 * Ensures all required tables exist with correct columns
 */

require_once 'config/database.php';

echo "<h1>Database Structure Fix</h1>";

try {
    $database = new Database();
    $db = $database->getConnection();
    
    if (!$db) {
        throw new Exception("Database connection failed");
    }
    
    echo "<p>✅ Database connection successful</p>";
    
    // Check current table structure
    echo "<h2>Checking Current Database Structure</h2>";
    
    $sql = "SHOW TABLES";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo "<p><strong>Existing tables:</strong> " . implode(', ', $tables) . "</p>";
    
    // Check page_shares table specifically
    if (in_array('page_shares', $tables)) {
        echo "<h3>page_shares table structure:</h3>";
        $sql = "DESCRIBE page_shares";
        $stmt = $db->prepare($sql);
        $stmt->execute();
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
        foreach ($columns as $col) {
            echo "<tr>";
            echo "<td>{$col['Field']}</td>";
            echo "<td>{$col['Type']}</td>";
            echo "<td>{$col['Null']}</td>";
            echo "<td>{$col['Key']}</td>";
            echo "<td>{$col['Default']}</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // Check if page_id column exists
        $hasPageId = false;
        foreach ($columns as $col) {
            if ($col['Field'] === 'page_id') {
                $hasPageId = true;
                break;
            }
        }
        
        if (!$hasPageId) {
            echo "<p>❌ page_id column missing! Adding it...</p>";
            $sql = "ALTER TABLE page_shares ADD COLUMN page_id INT NOT NULL AFTER id";
            $db->exec($sql);
            echo "<p>✅ page_id column added</p>";
        } else {
            echo "<p>✅ page_id column exists</p>";
        }
        
    } else {
        echo "<p>❌ page_shares table missing! Creating it...</p>";
        
        $sql = "CREATE TABLE page_shares (
            id INT AUTO_INCREMENT PRIMARY KEY,
            page_id INT NOT NULL,
            share_token VARCHAR(64) NOT NULL UNIQUE,
            short_code VARCHAR(10) NOT NULL UNIQUE,
            title VARCHAR(255),
            description TEXT,
            password_hash VARCHAR(255),
            expires_at TIMESTAMP NULL,
            max_views INT NULL,
            view_count INT DEFAULT 0,
            allow_download BOOLEAN DEFAULT FALSE,
            show_forms BOOLEAN DEFAULT TRUE,
            show_metadata BOOLEAN DEFAULT FALSE,
            is_active BOOLEAN DEFAULT TRUE,
            created_by INT DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_page_id (page_id),
            INDEX idx_share_token (share_token),
            INDEX idx_short_code (short_code),
            INDEX idx_created_at (created_at),
            INDEX idx_is_active (is_active)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        $db->exec($sql);
        echo "<p>✅ page_shares table created</p>";
    }
    
    // Ensure form_submissions table exists
    echo "<h3>Checking form_submissions table:</h3>";
    
    if (!in_array('form_submissions', $tables)) {
        echo "<p>❌ form_submissions table missing! Creating it...</p>";
        
        $sql = "CREATE TABLE form_submissions (
            id INT AUTO_INCREMENT PRIMARY KEY,
            page_id INT NOT NULL,
            form_id INT NULL,
            share_id INT NULL,
            form_data JSON NOT NULL,
            ip_address VARCHAR(45),
            user_agent TEXT,
            referrer TEXT,
            submitted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            processed BOOLEAN DEFAULT FALSE,
            notes TEXT,
            INDEX idx_page_id (page_id),
            INDEX idx_form_id (form_id),
            INDEX idx_share_id (share_id),
            INDEX idx_submitted_at (submitted_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        $db->exec($sql);
        echo "<p>✅ form_submissions table created</p>";
    } else {
        echo "<p>✅ form_submissions table exists</p>";
    }
    
    // Ensure share_access_log table exists
    echo "<h3>Checking share_access_log table:</h3>";
    
    if (!in_array('share_access_log', $tables)) {
        echo "<p>❌ share_access_log table missing! Creating it...</p>";
        
        $sql = "CREATE TABLE share_access_log (
            id INT AUTO_INCREMENT PRIMARY KEY,
            share_id INT NOT NULL,
            ip_address VARCHAR(45),
            user_agent TEXT,
            referrer TEXT,
            access_type ENUM('view', 'form_submission', 'download') DEFAULT 'view',
            accessed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_share_id (share_id),
            INDEX idx_accessed_at (accessed_at),
            INDEX idx_access_type (access_type)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        $db->exec($sql);
        echo "<p>✅ share_access_log table created</p>";
    } else {
        echo "<p>✅ share_access_log table exists</p>";
    }
    
    // Test the problematic query
    echo "<h2>Testing Problematic Query</h2>";
    
    try {
        $sql = "SELECT ps.*, p.id as page_id, p.title as page_title 
                FROM page_shares ps 
                JOIN pages p ON ps.page_id = p.id 
                WHERE ps.share_token = ? AND ps.is_active = 1";
        $stmt = $db->prepare($sql);
        $stmt->execute(['test_token']);
        echo "<p>✅ Query syntax is correct</p>";
    } catch (Exception $e) {
        echo "<p>❌ Query error: " . $e->getMessage() . "</p>";
        
        // Try to identify the issue
        $sql = "SHOW COLUMNS FROM page_shares WHERE Field = 'page_id'";
        $stmt = $db->prepare($sql);
        $stmt->execute();
        $result = $stmt->fetch();
        
        if (!$result) {
            echo "<p>❌ page_id column definitely missing from page_shares table</p>";
            echo "<p>Attempting to add it...</p>";
            
            try {
                $sql = "ALTER TABLE page_shares ADD COLUMN page_id INT NOT NULL AFTER id";
                $db->exec($sql);
                echo "<p>✅ page_id column added successfully</p>";
            } catch (Exception $e2) {
                echo "<p>❌ Failed to add page_id column: " . $e2->getMessage() . "</p>";
            }
        }
    }
    
    // Show final table structure
    echo "<h2>Final Database Structure</h2>";
    
    $sql = "SHOW TABLES";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    foreach (['pages', 'page_shares', 'form_submissions', 'share_access_log'] as $table) {
        if (in_array($table, $tables)) {
            echo "<h4>$table table:</h4>";
            $sql = "DESCRIBE $table";
            $stmt = $db->prepare($sql);
            $stmt->execute();
            $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
            echo "<p>Columns: " . implode(', ', $columns) . "</p>";
        } else {
            echo "<h4>❌ $table table: MISSING</h4>";
        }
    }
    
    echo "<h2>Test Share Creation</h2>";
    
    // Try to create a test share
    $sql = "SELECT id FROM pages LIMIT 1";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $page = $stmt->fetch();
    
    if ($page) {
        echo "<p>Found test page with ID: {$page['id']}</p>";
        
        try {
            $shareToken = bin2hex(random_bytes(16));
            $shortCode = substr(str_shuffle('ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'), 0, 6);
            
            $sql = "INSERT INTO page_shares (page_id, share_token, short_code, title, show_forms, is_active) 
                    VALUES (?, ?, ?, ?, 1, 1)";
            $stmt = $db->prepare($sql);
            $stmt->execute([$page['id'], $shareToken, $shortCode, 'Test Share']);
            
            $shareId = $db->lastInsertId();
            echo "<p>✅ Test share created successfully with ID: $shareId</p>";
            echo "<p>Share token: $shareToken</p>";
            
        } catch (Exception $e) {
            echo "<p>❌ Failed to create test share: " . $e->getMessage() . "</p>";
        }
    } else {
        echo "<p>⚠️ No pages found to test with</p>";
    }
    
} catch (Exception $e) {
    echo "<p>❌ Error: " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "<h2>Quick Links</h2>";
echo "<p><a href='test_form_submission.php'>Test Form Submissions</a></p>";
echo "<p><a href='test_view_fix.php'>Test Sharing System</a></p>";
echo "<p><a href='index.html'>Main Application</a></p>";

?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
table { border-collapse: collapse; margin: 10px 0; }
th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
th { background-color: #f2f2f2; }
h1, h2, h3, h4 { color: #333; }
</style>
