<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Final Verification Test - Form Data & Delete Button</title>
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .test-container { max-width: 1200px; margin: 0 auto; }
        .test-section { background: white; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); margin: 20px 0; padding: 20px; }
        .btn { background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin: 5px; }
        .btn-success { background: #28a745; }
        .btn-danger { background: #dc3545; }
        .result { margin: 10px 0; padding: 15px; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .warning { background: #fff3cd; color: #856404; }
        .info { background: #d1ecf1; color: #0c5460; }
        
        /* Test-specific styles to ensure delete button visibility */
        .test-delete-btn {
            color: #dc3545 !important;
            background: transparent !important;
            border: none !important;
            padding: 8px !important;
            border-radius: 4px !important;
            cursor: pointer !important;
            display: inline-block !important;
            visibility: visible !important;
            opacity: 1 !important;
        }
        
        .test-delete-btn:hover {
            color: #c82333 !important;
            background: rgba(220, 53, 69, 0.1) !important;
        }
        
        .test-delete-btn i {
            display: inline-block !important;
            font-size: 14px !important;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Final Verification Test - Form Data & Delete Button</h1>
        
        <div class="result info">
            <h3>🔍 Comprehensive Verification</h3>
            <p>This test verifies that both the "empty or invalid data structure" issue and the missing delete button are resolved.</p>
        </div>

        <div class="test-section">
            <h2>1. Delete Button Visibility Test</h2>
            <p>Testing if delete buttons are properly rendered and styled:</p>
            
            <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;">
                <h4>Sample Delete Buttons:</h4>
                <button class="btn-icon btn-danger test-delete-btn" title="Delete Test 1">
                    <i class="fas fa-trash"></i>
                </button>
                <button class="btn-icon btn-danger" title="Delete Test 2">
                    <i class="fas fa-trash"></i>
                </button>
                <button class="test-delete-btn" title="Delete Test 3">
                    <i class="fas fa-trash"></i> Delete
                </button>
            </div>
            
            <div id="deleteButtonTest"></div>
        </div>

        <div class="test-section">
            <h2>2. Form Data Display Test</h2>
            <p>Testing form data parsing and preview generation:</p>
            
            <button onclick="testDataParsing()" class="btn btn-success">Test Data Parsing</button>
            <button onclick="loadRealSubmissions()" class="btn">Load Real Submissions</button>
            
            <div id="dataParsingTest"></div>
        </div>

        <div class="test-section">
            <h2>3. Full Integration Test</h2>
            <p>Testing the complete workflow with real data:</p>
            
            <button onclick="runFullTest()" class="btn btn-success">Run Full Integration Test</button>
            
            <div id="fullTestResult"></div>
        </div>

        <div class="test-section">
            <h2>4. Main Application Test</h2>
            <p>Direct link to test the main application:</p>
            
            <a href="index.html#database" target="_blank" class="btn">Open Main App Database Tab</a>
            <a href="debug_main_app.html" target="_blank" class="btn btn-success">Open Debug Version</a>
            
            <div style="margin: 15px 0; padding: 15px; background: #fff3cd; color: #856404; border-radius: 5px;">
                <h4>⚠️ Manual Verification Steps:</h4>
                <ol>
                    <li>Click "Open Main App Database Tab" above</li>
                    <li>Look for the submissions table</li>
                    <li>Verify that form data shows actual content (not "Invalid data")</li>
                    <li>Verify that delete buttons (trash icons) are visible in the Actions column</li>
                    <li>Try clicking a delete button to test functionality</li>
                </ol>
            </div>
        </div>

        <div class="test-section">
            <h2>5. Test Results Summary</h2>
            <div id="testSummary"></div>
        </div>
    </div>

    <script>
        let testResults = {
            deleteButtonVisible: false,
            dataParsingWorking: false,
            apiWorking: false,
            fullIntegrationWorking: false
        };

        // Test delete button visibility
        function testDeleteButtonVisibility() {
            const buttons = document.querySelectorAll('.test-delete-btn, .btn-icon.btn-danger');
            const resultDiv = document.getElementById('deleteButtonTest');
            
            let visibleCount = 0;
            let totalCount = buttons.length;
            
            buttons.forEach((button, index) => {
                const styles = window.getComputedStyle(button);
                const isVisible = styles.display !== 'none' && 
                                 styles.visibility !== 'hidden' && 
                                 styles.opacity !== '0';
                
                if (isVisible) {
                    visibleCount++;
                }
                
                console.log(`Button ${index + 1}:`, {
                    display: styles.display,
                    visibility: styles.visibility,
                    opacity: styles.opacity,
                    color: styles.color,
                    isVisible: isVisible
                });
            });
            
            testResults.deleteButtonVisible = visibleCount === totalCount;
            
            resultDiv.innerHTML = `
                <div class="result ${testResults.deleteButtonVisible ? 'success' : 'error'}">
                    <h4>${testResults.deleteButtonVisible ? '✅' : '❌'} Delete Button Visibility Test</h4>
                    <p><strong>Visible buttons:</strong> ${visibleCount} / ${totalCount}</p>
                    <p><strong>Result:</strong> ${testResults.deleteButtonVisible ? 'All buttons visible' : 'Some buttons not visible'}</p>
                </div>
            `;
            
            updateTestSummary();
        }

        // Test data parsing
        function testDataParsing() {
            const resultDiv = document.getElementById('dataParsingTest');
            resultDiv.innerHTML = '<div class="result info">Testing data parsing...</div>';
            
            const testCases = [
                {
                    name: 'Valid JSON Object',
                    data: '{"name":"John Doe","email":"<EMAIL>","message":"Test message"}',
                    expected: 'name: John Doe, email: <EMAIL>, message: Test message'
                },
                {
                    name: 'Empty Object',
                    data: '{}',
                    expected: 'Empty form'
                },
                {
                    name: 'Null Data',
                    data: null,
                    expected: 'No data'
                },
                {
                    name: 'Invalid JSON',
                    data: '{invalid json}',
                    expected: 'Invalid data format'
                },
                {
                    name: 'Already Parsed Object',
                    data: {name: 'Jane Doe', email: '<EMAIL>'},
                    expected: 'name: Jane Doe, email: <EMAIL>'
                }
            ];
            
            let passedTests = 0;
            let totalTests = testCases.length;
            let results = '';
            
            testCases.forEach(testCase => {
                try {
                    const result = formatSubmissionPreview(testCase.data);
                    const passed = result.includes(testCase.expected.split(',')[0]) || result === testCase.expected;
                    
                    if (passed) passedTests++;
                    
                    results += `
                        <div style="background: ${passed ? '#d4edda' : '#f8d7da'}; color: ${passed ? '#155724' : '#721c24'}; padding: 10px; margin: 5px 0; border-radius: 5px;">
                            <strong>${testCase.name}:</strong> ${passed ? '✅ PASS' : '❌ FAIL'}<br>
                            <strong>Expected:</strong> ${testCase.expected}<br>
                            <strong>Got:</strong> ${result}
                        </div>
                    `;
                } catch (error) {
                    results += `
                        <div style="background: #f8d7da; color: #721c24; padding: 10px; margin: 5px 0; border-radius: 5px;">
                            <strong>${testCase.name}:</strong> ❌ ERROR<br>
                            <strong>Error:</strong> ${error.message}
                        </div>
                    `;
                }
            });
            
            testResults.dataParsingWorking = passedTests === totalTests;
            
            resultDiv.innerHTML = `
                <div class="result ${testResults.dataParsingWorking ? 'success' : 'error'}">
                    <h4>${testResults.dataParsingWorking ? '✅' : '❌'} Data Parsing Test</h4>
                    <p><strong>Passed:</strong> ${passedTests} / ${totalTests}</p>
                </div>
                ${results}
            `;
            
            updateTestSummary();
        }

        // Format submission preview (same as main app)
        function formatSubmissionPreview(formDataJson) {
            try {
                // Handle null, undefined, or empty values
                if (!formDataJson || formDataJson === 'null' || formDataJson === '') {
                    return 'No data';
                }
                
                // If it's already an object, use it directly
                let data;
                if (typeof formDataJson === 'object') {
                    data = formDataJson;
                } else {
                    // Try to parse as JSON
                    data = JSON.parse(formDataJson);
                }
                
                // Check if data is empty
                if (!data || typeof data !== 'object' || Object.keys(data).length === 0) {
                    return 'Empty form';
                }
                
                const fields = Object.keys(data).slice(0, 3); // Show first 3 fields
                const preview = fields.map(field => {
                    const value = data[field];
                    const displayValue = value && value.length > 20 ? value.substring(0, 20) + '...' : value;
                    return `${field}: ${displayValue}`;
                }).join(', ');
                
                return preview.length > 50 ? preview.substring(0, 50) + '...' : preview;
            } catch (error) {
                console.error('Error formatting submission preview:', error, 'Data:', formDataJson);
                return 'Invalid data format';
            }
        }

        // Load real submissions
        async function loadRealSubmissions() {
            const resultDiv = document.getElementById('dataParsingTest');
            resultDiv.innerHTML = '<div class="result info">Loading real submissions...</div>';
            
            try {
                const formData = new FormData();
                formData.append('action', 'get_submissions');
                
                const response = await fetch('includes/sharing_manager.php', {
                    method: 'POST',
                    body: formData
                });
                const data = await response.json();
                
                if (data.success) {
                    testResults.apiWorking = true;
                    
                    let html = `
                        <div class="result success">
                            <h4>✅ Real Submissions Loaded</h4>
                            <p><strong>Total:</strong> ${data.total}</p>
                            <p><strong>Loaded:</strong> ${data.submissions.length}</p>
                        </div>
                    `;
                    
                    if (data.submissions.length > 0) {
                        html += '<h4>Sample Submissions:</h4>';
                        
                        data.submissions.slice(0, 3).forEach(submission => {
                            const preview = formatSubmissionPreview(submission.form_data);
                            const isValidPreview = preview !== 'Invalid data format' && preview !== 'Empty form' && preview !== 'No data';
                            
                            html += `
                                <div style="background: ${isValidPreview ? '#d4edda' : '#fff3cd'}; color: ${isValidPreview ? '#155724' : '#856404'}; padding: 10px; margin: 5px 0; border-radius: 5px;">
                                    <strong>ID ${submission.id}:</strong> ${isValidPreview ? '✅' : '⚠️'} ${preview}
                                    <br><strong>Raw:</strong> ${submission.form_data ? submission.form_data.substring(0, 100) + '...' : 'null'}
                                </div>
                            `;
                        });
                    }
                    
                    resultDiv.innerHTML = html;
                } else {
                    testResults.apiWorking = false;
                    resultDiv.innerHTML = `
                        <div class="result error">
                            <h4>❌ Failed to Load Submissions</h4>
                            <p>${data.message}</p>
                        </div>
                    `;
                }
            } catch (error) {
                testResults.apiWorking = false;
                resultDiv.innerHTML = `
                    <div class="result error">
                        <h4>❌ API Error</h4>
                        <p>${error.message}</p>
                    </div>
                `;
            }
            
            updateTestSummary();
        }

        // Run full integration test
        async function runFullTest() {
            const resultDiv = document.getElementById('fullTestResult');
            resultDiv.innerHTML = '<div class="result info">Running full integration test...</div>';
            
            try {
                // Test API
                const formData = new FormData();
                formData.append('action', 'get_submissions');
                
                const response = await fetch('includes/sharing_manager.php', {
                    method: 'POST',
                    body: formData
                });
                const data = await response.json();
                
                if (data.success && data.submissions.length > 0) {
                    // Test viewing a specific submission
                    const submissionId = data.submissions[0].id;
                    
                    const viewFormData = new FormData();
                    viewFormData.append('action', 'get_submission');
                    viewFormData.append('submission_id', submissionId);
                    
                    const viewResponse = await fetch('includes/sharing_manager.php', {
                        method: 'POST',
                        body: viewFormData
                    });
                    const viewData = await viewResponse.json();
                    
                    if (viewData.success) {
                        testResults.fullIntegrationWorking = true;
                        
                        const preview = formatSubmissionPreview(viewData.submission.form_data);
                        
                        resultDiv.innerHTML = `
                            <div class="result success">
                                <h4>✅ Full Integration Test Passed</h4>
                                <p><strong>API:</strong> ✅ Working</p>
                                <p><strong>Data Loading:</strong> ✅ Working</p>
                                <p><strong>Individual View:</strong> ✅ Working</p>
                                <p><strong>Data Preview:</strong> ${preview}</p>
                                <p><strong>Sample Submission ID:</strong> ${submissionId}</p>
                            </div>
                        `;
                    } else {
                        throw new Error('Failed to view individual submission');
                    }
                } else {
                    throw new Error('No submissions available for testing');
                }
            } catch (error) {
                testResults.fullIntegrationWorking = false;
                resultDiv.innerHTML = `
                    <div class="result error">
                        <h4>❌ Full Integration Test Failed</h4>
                        <p>${error.message}</p>
                    </div>
                `;
            }
            
            updateTestSummary();
        }

        // Update test summary
        function updateTestSummary() {
            const summaryDiv = document.getElementById('testSummary');
            const totalTests = Object.keys(testResults).length;
            const passedTests = Object.values(testResults).filter(result => result === true).length;
            
            let html = `
                <div class="result ${passedTests === totalTests ? 'success' : 'warning'}">
                    <h4>${passedTests === totalTests ? '✅' : '⚠️'} Test Summary</h4>
                    <p><strong>Passed:</strong> ${passedTests} / ${totalTests}</p>
                    <ul>
                        <li>Delete Button Visible: ${testResults.deleteButtonVisible ? '✅' : '❌'}</li>
                        <li>Data Parsing Working: ${testResults.dataParsingWorking ? '✅' : '❌'}</li>
                        <li>API Working: ${testResults.apiWorking ? '✅' : '❌'}</li>
                        <li>Full Integration Working: ${testResults.fullIntegrationWorking ? '✅' : '❌'}</li>
                    </ul>
                </div>
            `;
            
            if (passedTests === totalTests) {
                html += `
                    <div class="result success">
                        <h4>🎉 All Tests Passed!</h4>
                        <p>Both the "empty or invalid data structure" issue and the missing delete button have been resolved.</p>
                        <p><strong>You can now use the main application with confidence.</strong></p>
                    </div>
                `;
            }
            
            summaryDiv.innerHTML = html;
        }

        // Run initial tests on page load
        window.addEventListener('load', function() {
            setTimeout(() => {
                testDeleteButtonVisibility();
                testDataParsing();
            }, 500);
        });
    </script>
</body>
</html>
