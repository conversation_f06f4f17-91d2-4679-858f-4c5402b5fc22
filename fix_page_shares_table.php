<?php
/**
 * Direct Fix for page_shares Table
 * Adds missing page_id column and ensures proper structure
 */

require_once 'config/database.php';

echo "<h1>Fixing page_shares Table</h1>";

try {
    $database = new Database();
    $db = $database->getConnection();
    
    if (!$db) {
        throw new Exception("Database connection failed");
    }
    
    echo "<p>✅ Database connection successful</p>";
    
    // Check if page_shares table exists
    $sql = "SHOW TABLES LIKE 'page_shares'";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $tableExists = $stmt->fetch();
    
    if (!$tableExists) {
        echo "<p>❌ page_shares table doesn't exist. Creating it...</p>";
        
        $sql = "CREATE TABLE page_shares (
            id INT AUTO_INCREMENT PRIMARY KEY,
            page_id INT NOT NULL,
            share_token VARCHAR(64) NOT NULL UNIQUE,
            short_code VARCHAR(10) NOT NULL UNIQUE,
            title VARCHAR(255),
            description TEXT,
            password_hash VARCHAR(255),
            expires_at TIMESTAMP NULL,
            max_views INT NULL,
            view_count INT DEFAULT 0,
            allow_download BOOLEAN DEFAULT FALSE,
            show_forms BOOLEAN DEFAULT TRUE,
            show_metadata BOOLEAN DEFAULT FALSE,
            is_active BOOLEAN DEFAULT TRUE,
            created_by INT DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_page_id (page_id),
            INDEX idx_share_token (share_token),
            INDEX idx_short_code (short_code),
            INDEX idx_created_at (created_at),
            INDEX idx_is_active (is_active)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        $db->exec($sql);
        echo "<p>✅ page_shares table created successfully</p>";
        
    } else {
        echo "<p>✅ page_shares table exists</p>";
        
        // Check current structure
        $sql = "DESCRIBE page_shares";
        $stmt = $db->prepare($sql);
        $stmt->execute();
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<h3>Current Table Structure:</h3>";
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
        
        $hasPageId = false;
        foreach ($columns as $col) {
            echo "<tr>";
            echo "<td>{$col['Field']}</td>";
            echo "<td>{$col['Type']}</td>";
            echo "<td>{$col['Null']}</td>";
            echo "<td>{$col['Key']}</td>";
            echo "<td>{$col['Default']}</td>";
            echo "</tr>";
            
            if ($col['Field'] === 'page_id') {
                $hasPageId = true;
            }
        }
        echo "</table>";
        
        if (!$hasPageId) {
            echo "<p>❌ page_id column is missing! Adding it...</p>";
            
            try {
                $sql = "ALTER TABLE page_shares ADD COLUMN page_id INT NOT NULL AFTER id";
                $db->exec($sql);
                echo "<p>✅ page_id column added successfully</p>";
                
                // Add index for page_id
                $sql = "ALTER TABLE page_shares ADD INDEX idx_page_id (page_id)";
                $db->exec($sql);
                echo "<p>✅ Index added for page_id column</p>";
                
            } catch (Exception $e) {
                echo "<p>❌ Error adding page_id column: " . $e->getMessage() . "</p>";
                
                // Try alternative approach - recreate table
                echo "<p>Trying to recreate table...</p>";
                
                // Backup existing data
                $sql = "SELECT * FROM page_shares";
                $stmt = $db->prepare($sql);
                $stmt->execute();
                $existingData = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                // Drop and recreate table
                $sql = "DROP TABLE page_shares";
                $db->exec($sql);
                
                $sql = "CREATE TABLE page_shares (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    page_id INT NOT NULL,
                    share_token VARCHAR(64) NOT NULL UNIQUE,
                    short_code VARCHAR(10) NOT NULL UNIQUE,
                    title VARCHAR(255),
                    description TEXT,
                    password_hash VARCHAR(255),
                    expires_at TIMESTAMP NULL,
                    max_views INT NULL,
                    view_count INT DEFAULT 0,
                    allow_download BOOLEAN DEFAULT FALSE,
                    show_forms BOOLEAN DEFAULT TRUE,
                    show_metadata BOOLEAN DEFAULT FALSE,
                    is_active BOOLEAN DEFAULT TRUE,
                    created_by INT DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    INDEX idx_page_id (page_id),
                    INDEX idx_share_token (share_token),
                    INDEX idx_short_code (short_code),
                    INDEX idx_created_at (created_at),
                    INDEX idx_is_active (is_active)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
                
                $db->exec($sql);
                echo "<p>✅ Table recreated successfully</p>";
                
                // Restore data (if any existed and had page_id)
                if (!empty($existingData)) {
                    echo "<p>Attempting to restore existing data...</p>";
                    foreach ($existingData as $row) {
                        if (isset($row['page_id'])) {
                            // Data has page_id, restore it
                            $sql = "INSERT INTO page_shares (page_id, share_token, short_code, title, description, password_hash, expires_at, max_views, view_count, allow_download, show_forms, show_metadata, is_active, created_by, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
                            $stmt = $db->prepare($sql);
                            $stmt->execute([
                                $row['page_id'],
                                $row['share_token'],
                                $row['short_code'],
                                $row['title'],
                                $row['description'],
                                $row['password_hash'],
                                $row['expires_at'],
                                $row['max_views'],
                                $row['view_count'],
                                $row['allow_download'] ?? 0,
                                $row['show_forms'] ?? 1,
                                $row['show_metadata'] ?? 0,
                                $row['is_active'] ?? 1,
                                $row['created_by'] ?? 1,
                                $row['created_at'],
                                $row['updated_at']
                            ]);
                        }
                    }
                    echo "<p>✅ Data restored</p>";
                }
            }
        } else {
            echo "<p>✅ page_id column exists</p>";
        }
    }
    
    // Verify the fix by testing the problematic query
    echo "<h2>Testing the Fixed Query</h2>";
    
    try {
        $sql = "SELECT ps.*, p.id as page_id, p.title as page_title 
                FROM page_shares ps 
                JOIN pages p ON ps.page_id = p.id 
                WHERE ps.share_token = ? AND ps.is_active = 1";
        $stmt = $db->prepare($sql);
        $stmt->execute(['test_token_that_does_not_exist']);
        
        echo "<p>✅ Query executed successfully (no syntax errors)</p>";
        
    } catch (Exception $e) {
        echo "<p>❌ Query still has errors: " . $e->getMessage() . "</p>";
    }
    
    // Create a test share to verify everything works
    echo "<h2>Creating Test Share</h2>";
    
    // Get a page to test with
    $sql = "SELECT id, title, original_filename FROM pages LIMIT 1";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $page = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($page) {
        echo "<p>Using page: {$page['title']} (ID: {$page['id']})</p>";
        
        try {
            $shareToken = bin2hex(random_bytes(16));
            $shortCode = substr(str_shuffle('ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'), 0, 6);
            
            $sql = "INSERT INTO page_shares (page_id, share_token, short_code, title, show_forms, is_active) 
                    VALUES (?, ?, ?, ?, 1, 1)";
            $stmt = $db->prepare($sql);
            $stmt->execute([$page['id'], $shareToken, $shortCode, 'Test Share - Fixed']);
            
            $shareId = $db->lastInsertId();
            echo "<p>✅ Test share created successfully with ID: $shareId</p>";
            
            // Test the query that was failing
            $sql = "SELECT ps.*, p.id as page_id, p.title as page_title 
                    FROM page_shares ps 
                    JOIN pages p ON ps.page_id = p.id 
                    WHERE ps.share_token = ? AND ps.is_active = 1";
            $stmt = $db->prepare($sql);
            $stmt->execute([$shareToken]);
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($result) {
                echo "<p>✅ Query test successful! Found share: {$result['title']}</p>";
                
                // Generate share URL
                $baseUrl = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http') . 
                          '://' . $_SERVER['HTTP_HOST'] . 
                          rtrim(dirname($_SERVER['SCRIPT_NAME']), '/');
                $shareUrl = $baseUrl . '/view.php?token=' . $shareToken;
                
                echo "<p><strong>Test Share URL:</strong> <a href='$shareUrl' target='_blank'>$shareUrl</a></p>";
                
            } else {
                echo "<p>❌ Query test failed - no results returned</p>";
            }
            
        } catch (Exception $e) {
            echo "<p>❌ Error creating test share: " . $e->getMessage() . "</p>";
        }
        
    } else {
        echo "<p>⚠️ No pages found to test with</p>";
    }
    
    // Show final table structure
    echo "<h2>Final Table Structure</h2>";
    $sql = "DESCRIBE page_shares";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    foreach ($columns as $col) {
        echo "<tr>";
        echo "<td>{$col['Field']}</td>";
        echo "<td>{$col['Type']}</td>";
        echo "<td>{$col['Null']}</td>";
        echo "<td>{$col['Key']}</td>";
        echo "<td>{$col['Default']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
} catch (Exception $e) {
    echo "<p>❌ Error: " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "<h2>Next Steps</h2>";
echo "<p>Now try testing the form submission again:</p>";
echo "<ul>";
echo "<li><a href='test_submit_form.php'>Test Form Submission</a></li>";
echo "<li><a href='test_form_submission.php'>Complete Form System Test</a></li>";
echo "<li><a href='index.html'>Main Application</a></li>";
echo "</ul>";

?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
table { border-collapse: collapse; margin: 10px 0; }
th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
th { background-color: #f2f2f2; }
h1, h2, h3 { color: #333; }
</style>
