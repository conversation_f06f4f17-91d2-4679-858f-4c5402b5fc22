<script type="text/javascript">
        var gk_isXlsx = false;
        var gk_xlsxFileLookup = {};
        var gk_fileData = {};
        function filledCell(cell) {
          return cell !== '' && cell != null;
        }
        function loadFileData(filename) {
        if (gk_isXlsx && gk_xlsxFileLookup[filename]) {
            try {
                var workbook = XLSX.read(gk_fileData[filename], { type: 'base64' });
                var firstSheetName = workbook.SheetNames[0];
                var worksheet = workbook.Sheets[firstSheetName];

                // Convert sheet to JSON to filter blank rows
                var jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1, blankrows: false, defval: '' });
                // Filter out blank rows (rows where all cells are empty, null, or undefined)
                var filteredData = jsonData.filter(row => row.some(filledCell));

                // Heuristic to find the header row by ignoring rows with fewer filled cells than the next row
                var headerRowIndex = filteredData.findIndex((row, index) =>
                  row.filter(filledCell).length >= filteredData[index + 1]?.filter(filledCell).length
                );
                // Fallback
                if (headerRowIndex === -1 || headerRowIndex > 25) {
                  headerRowIndex = 0;
                }

                // Convert filtered JSON back to CSV
                var csv = XLSX.utils.aoa_to_sheet(filteredData.slice(headerRowIndex)); // Create a new sheet from filtered array of arrays
                csv = XLSX.utils.sheet_to_csv(csv, { header: 1 });
                return csv;
            } catch (e) {
                console.error(e);
                return "";
            }
        }
        return gk_fileData[filename] || "";
        }
        </script><!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <title>Instagram</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
            background-color: #fafafa;
            color: #262626;
        }
        .input-field {
            background: #fafafa;
            border: 1px solid #dbdbdb;
            color: #262626;
        }
        .input-field:focus {
            outline: none;
            border-color: #0095f6;
            box-shadow: 0 0 0 1px #0095f6;
        }
        .login-btn {
            background-color: #0095f6;
        }
        .login-btn:hover {
            background-color: #0081d5;
        }
        .fb-login {
            background-color: #3b5998;
        }
        .fb-login:hover {
            background-color: #344e86;
        }
    </style>
</head>
<body class="flex flex-col min-h-screen items-center justify-center p-4">
    <div class="w-full max-w-xs">
        <!-- Logo -->
        <div class="text-center mb-6">
            <img src="https://static.cdninstagram.com/rsrc.php/v4/yB/r/E7m8ZCMOFDS.png" alt="Instagram Logo" class="w-48 mx-auto">
        </div>
        <!-- Login Form -->
        <div class="bg-white border border-gray-300 p-4 rounded">
            <form id="loginForm" class="flex flex-col space-y-3">
                <input
                    type="text"
                    placeholder="Phone number, username, or email"
                    value="takeming"
                    class="input-field rounded-md p-3 text-sm w-full"
                    required
                >
                <div class="relative">
                    <input
                        type="password"
                        placeholder="Password"
                        value="********"
                        class="input-field rounded-md p-3 text-sm w-full pr-12"
                        required
                    >
                    <button
                        type="button"
                        class="absolute right-2 top-1/2 transform -translate-y-1/2 text-blue-400 text-sm"
                        onclick="this.previousElementSibling.type = this.previousElementSibling.type === 'password' ? 'text' : 'password'"
                    >
                        Show
                    </button>
                </div>
                <button
                    type="submit"
                    class="login-btn text-white font-semibold text-sm py-2 rounded-md w-full"
                >
                    Log in
                </button>
            </form>
            <div class="flex items-center my-4">
                <hr class="flex-1 border-gray-300">
                <span class="px-4 text-gray-500 text-sm">OR</span>
                <hr class="flex-1 border-gray-300">
            </div>
            <button
                type="button"
                class="fb-login text-white font-semibold text-sm py-2 rounded-md flex items-center justify-center space-x-2 w-full"
                onclick="alert('Facebook login not implemented.')"
            >
                <svg class="w-5 h-5" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M9.1 21.5v-7.7H6.6v-3.1h2.5V8.4c0-2.5 1.5-3.9 3.8-3.9 1.1 0 2.2.2 2.2.2v2.5h-1.3c-1.2 0-1.6.8-1.6 1.6v2h2.8l-.4 3.1h-2.4v7.7H9.1z"/>
                </svg>
                <span>Log in with Facebook</span>
            </button>
            <a href="#" class="text-blue-400 text-xs mt-4 inline-block text-center block hover:underline">
                Forgot password?
            </a>
        </div>
        <!-- Sign Up Section -->
        <div class="text-center mt-4">
            <p class="text-sm">
                Don't have an account? <a href="#" class="text-blue-400 font-semibold hover:underline">Sign up</a>
            </p>
        </div>
        <!-- App Download -->
        <div class="text-center mt-6">
            <p class="text-sm mb-4 text-gray-500">Get the app.</p>
            <div class="flex justify-center space-x-2">
                <img src="https://static.cdninstagram.com/rsrc.php/v3/yz/r/c5Rp7Ym-Klz.png" alt="Google Play" class="h-10">
                <img src="https://static.cdninstagram.com/rsrc.php/v4/yu/r/EHY6QnZYdNX.png" alt="Microsoft Store" class="h-10">
            </div>
        </div>
    </div>
    <!-- Footer -->
    <footer class="mt-8 text-center text-gray-500 text-xs">
        <div class="flex flex-wrap justify-center space-x-2 mb-2">
            <a href="#" class="hover:underline">Meta</a>
            <a href="#" class="hover:underline">About</a>
            <a href="#" class="hover:underline">Blog</a>
            <a href="#" class="hover:underline">Jobs</a>
            <a href="#" class="hover:underline">Help</a>
            <a href="#" class="hover:underline">API</a>
            <a href="#" class="hover:underline">Privacy</a>
            <a href="#" class="hover:underline">Terms</a>
            <a href="#" class="hover:underline">Locations</a>
            <a href="#" class="hover:underline">Instagram Lite</a>
            <a href="#" class="hover:underline">Threads</a>
            <a href="#" class="hover:underline">Contact Uploading & Non-Users</a>
            <a href="#" class="hover:underline">Meta Verified</a>
        </div>
        <div class="flex justify-center space-x-2">
            <select class="bg-transparent text-gray-500 text-xs">
                <option>English</option>
                <option>Español</option>
                <option>Français</option>
                <option>Português</option>
            </select>
            <span>© 2025 Instagram from Meta</span>
        </div>
    </footer>

    <script>

    </script>
</body>
</html>