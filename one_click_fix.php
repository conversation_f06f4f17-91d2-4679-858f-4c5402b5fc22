<?php
/**
 * One-Click Database Fix
 * Simple script to fix the page_id column issue
 */

require_once 'config/database.php';

echo "<h1>One-Click Database Fix</h1>";

try {
    $database = new Database();
    $db = $database->getConnection();
    
    if (!$db) {
        throw new Exception("Database connection failed");
    }
    
    echo "<p>✅ Database connected</p>";
    
    // Drop and recreate page_shares table with correct structure
    echo "<p>🔧 Fixing page_shares table...</p>";
    
    $db->exec("DROP TABLE IF EXISTS page_shares");
    
    $sql = "CREATE TABLE page_shares (
        id INT AUTO_INCREMENT PRIMARY KEY,
        page_id INT NOT NULL,
        share_token VARCHAR(64) NOT NULL UNIQUE,
        short_code VARCHAR(10) NOT NULL UNIQUE,
        title VARCHAR(255),
        description TEXT,
        password_hash VARCHAR(255),
        expires_at TIMESTAMP NULL,
        max_views INT NULL,
        view_count INT DEFAULT 0,
        allow_download BOOLEAN DEFAULT FALSE,
        show_forms BOOLEAN DEFAULT TRUE,
        show_metadata BOOLEAN DEFAULT FALSE,
        is_active BOOLEAN DEFAULT TRUE,
        created_by INT DEFAULT 1,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_page_id (page_id),
        INDEX idx_share_token (share_token),
        INDEX idx_short_code (short_code),
        INDEX idx_created_at (created_at),
        INDEX idx_is_active (is_active)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $db->exec($sql);
    echo "<p>✅ page_shares table fixed</p>";
    
    // Ensure form_submissions table exists
    echo "<p>🔧 Ensuring form_submissions table...</p>";
    
    $sql = "CREATE TABLE IF NOT EXISTS form_submissions (
        id INT AUTO_INCREMENT PRIMARY KEY,
        page_id INT NOT NULL,
        form_id INT NULL,
        share_id INT NULL,
        form_data JSON NOT NULL,
        ip_address VARCHAR(45),
        user_agent TEXT,
        referrer TEXT,
        submitted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        processed BOOLEAN DEFAULT FALSE,
        notes TEXT,
        INDEX idx_page_id (page_id),
        INDEX idx_form_id (form_id),
        INDEX idx_share_id (share_id),
        INDEX idx_submitted_at (submitted_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $db->exec($sql);
    echo "<p>✅ form_submissions table ready</p>";
    
    // Test the fix
    echo "<p>🧪 Testing the fix...</p>";
    
    $sql = "SELECT ps.*, p.id as page_id, p.title as page_title 
            FROM page_shares ps 
            JOIN pages p ON ps.page_id = p.id 
            WHERE ps.share_token = ? AND ps.is_active = 1";
    $stmt = $db->prepare($sql);
    $stmt->execute(['test_token']);
    
    echo "<p>✅ Query test passed - no more column errors!</p>";
    
    echo "<div style='background: #d4edda; color: #155724; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h2>🎉 Fix Complete!</h2>";
    echo "<p>The database has been fixed. The 'page_id' column error should now be resolved.</p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h2>❌ Fix Failed</h2>";
    echo "<p>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p>Please try the manual fix instructions in MANUAL_DATABASE_FIX.md</p>";
    echo "</div>";
}

echo "<h2>Next Steps</h2>";
echo "<p>Now test the system:</p>";
echo "<ul>";
echo "<li><a href='test_form_submission.php'>Test Form Submission System</a></li>";
echo "<li><a href='test_submit_form.php'>Test Submit Form Handler</a></li>";
echo "<li><a href='index.html'>Main Application</a></li>";
echo "</ul>";

?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h1, h2 { color: #333; }
</style>
