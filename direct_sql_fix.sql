-- Direct SQL Fix for page_id Column Issue
-- Copy and paste this entire script into phpMyAdmin SQL tab

-- Step 1: Disable foreign key checks
SET FOREIGN_KEY_CHECKS = 0;

-- Step 2: Check current structure (this will show what columns exist)
DESCRIBE page_shares;

-- Step 3: Drop all related tables to avoid foreign key issues
DROP TABLE IF EXISTS share_access_log;
DROP TABLE IF EXISTS form_submissions;
DROP TABLE IF EXISTS page_shares;

-- Step 4: Create page_shares table with correct structure
CREATE TABLE page_shares (
    id INT AUTO_INCREMENT PRIMARY KEY,
    page_id INT NOT NULL,
    share_token VARCHAR(64) NOT NULL UNIQUE,
    short_code VARCHAR(10) NOT NULL UNIQUE,
    title VARCHAR(255),
    description TEXT,
    password_hash VARCHAR(255),
    expires_at TIMESTAMP NULL,
    max_views INT NULL,
    view_count INT DEFAULT 0,
    allow_download BOOLEAN DEFAULT FALSE,
    show_forms BOOLEAN DEFAULT TRUE,
    show_metadata BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    created_by INT DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_page_id (page_id),
    INDEX idx_share_token (share_token),
    INDEX idx_short_code (short_code),
    INDEX idx_created_at (created_at),
    INDEX idx_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Step 5: Create form_submissions table
CREATE TABLE form_submissions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    page_id INT NOT NULL,
    form_id INT NULL,
    share_id INT NULL,
    form_data JSON NOT NULL,
    ip_address VARCHAR(45),
    user_agent TEXT,
    referrer TEXT,
    submitted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    processed BOOLEAN DEFAULT FALSE,
    notes TEXT,
    INDEX idx_page_id (page_id),
    INDEX idx_form_id (form_id),
    INDEX idx_share_id (share_id),
    INDEX idx_submitted_at (submitted_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Step 6: Create share_access_log table
CREATE TABLE share_access_log (
    id INT AUTO_INCREMENT PRIMARY KEY,
    share_id INT NOT NULL,
    ip_address VARCHAR(45),
    user_agent TEXT,
    referrer TEXT,
    access_type ENUM('view', 'form_submission', 'download') DEFAULT 'view',
    accessed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_share_id (share_id),
    INDEX idx_accessed_at (accessed_at),
    INDEX idx_access_type (access_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Step 7: Re-enable foreign key checks
SET FOREIGN_KEY_CHECKS = 1;

-- Step 8: Verify the fix - this should show page_id column
DESCRIBE page_shares;

-- Step 9: Test the problematic query (should not produce errors)
SELECT ps.*, p.id as page_id, p.title as page_title 
FROM page_shares ps 
JOIN pages p ON ps.page_id = p.id 
WHERE ps.share_token = 'test_token' AND ps.is_active = 1;

-- Step 10: Show all tables
SHOW TABLES;
