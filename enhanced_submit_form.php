<?php
/**
 * Enhanced Form Submission Handler
 * Processes form submissions from shared pages with full data collection
 * Works both locally and when deployed online
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

require_once 'config/database.php';

/**
 * Get real IP address (works with proxies and load balancers)
 */
function getRealIpAddress() {
    $ipKeys = ['HTTP_CF_CONNECTING_IP', 'HTTP_X_FORWARDED_FOR', 'HTTP_X_FORWARDED', 
               'HTTP_X_CLUSTER_CLIENT_IP', 'HTTP_FORWARDED_FOR', 'HTTP_FORWARDED', 
               'REMOTE_ADDR'];
    
    foreach ($ipKeys as $key) {
        if (array_key_exists($key, $_SERVER) === true) {
            $ip = $_SERVER[$key];
            if (strpos($ip, ',') !== false) {
                $ip = explode(',', $ip)[0];
            }
            $ip = trim($ip);
            if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                return $ip;
            }
        }
    }
    return $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1';
}

/**
 * Parse user agent for browser and device information
 */
function parseUserAgent($userAgent) {
    $info = [
        'browser_name' => 'Unknown',
        'browser_version' => '',
        'os_name' => 'Unknown',
        'device_type' => 'desktop'
    ];
    
    // Browser detection
    if (preg_match('/Chrome\/([0-9.]+)/', $userAgent, $matches)) {
        $info['browser_name'] = 'Chrome';
        $info['browser_version'] = $matches[1];
    } elseif (preg_match('/Firefox\/([0-9.]+)/', $userAgent, $matches)) {
        $info['browser_name'] = 'Firefox';
        $info['browser_version'] = $matches[1];
    } elseif (preg_match('/Safari\/([0-9.]+)/', $userAgent, $matches)) {
        $info['browser_name'] = 'Safari';
        $info['browser_version'] = $matches[1];
    } elseif (preg_match('/Edge\/([0-9.]+)/', $userAgent, $matches)) {
        $info['browser_name'] = 'Edge';
        $info['browser_version'] = $matches[1];
    }
    
    // OS detection
    if (strpos($userAgent, 'Windows') !== false) {
        $info['os_name'] = 'Windows';
    } elseif (strpos($userAgent, 'Mac') !== false) {
        $info['os_name'] = 'macOS';
    } elseif (strpos($userAgent, 'Linux') !== false) {
        $info['os_name'] = 'Linux';
    } elseif (strpos($userAgent, 'Android') !== false) {
        $info['os_name'] = 'Android';
    } elseif (strpos($userAgent, 'iOS') !== false) {
        $info['os_name'] = 'iOS';
    }
    
    // Device type detection
    if (preg_match('/Mobile|Android|iPhone|iPad/', $userAgent)) {
        if (preg_match('/iPad|Tablet/', $userAgent)) {
            $info['device_type'] = 'tablet';
        } else {
            $info['device_type'] = 'mobile';
        }
    }
    
    return $info;
}

/**
 * Generate visitor session ID
 */
function generateVisitorSession($ip, $userAgent) {
    return hash('sha256', $ip . $userAgent . date('Y-m-d'));
}

try {
    $database = new Database();
    $db = $database->getConnection();
    
    if (!$db) {
        throw new Exception("Database connection failed");
    }
    
    // Get form data
    $shareToken = $_POST['_share_token'] ?? null;
    $formId = $_POST['_form_id'] ?? null;
    $pageId = $_POST['_page_id'] ?? null;
    $formName = $_POST['_form_name'] ?? 'unnamed_form';
    
    if (!$shareToken && !$pageId) {
        throw new Exception("Share token or page ID is required");
    }
    
    // Validate share token and get share info
    $shareInfo = null;
    if ($shareToken) {
        $sql = "SELECT ps.*, p.id as page_id, p.title as page_title 
                FROM page_shares ps 
                JOIN pages p ON ps.page_id = p.id 
                WHERE ps.share_token = ? AND ps.is_active = 1";
        $stmt = $db->prepare($sql);
        $stmt->execute([$shareToken]);
        $shareInfo = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$shareInfo) {
            throw new Exception("Invalid or expired share token");
        }
        
        // Check if forms are enabled for this share
        if (!$shareInfo['show_forms']) {
            throw new Exception("Form submissions are disabled for this share");
        }
        
        // Check expiration
        if ($shareInfo['expires_at'] && strtotime($shareInfo['expires_at']) < time()) {
            throw new Exception("This share has expired");
        }
        
        // Check view limit
        if ($shareInfo['max_views'] && $shareInfo['view_count'] >= $shareInfo['max_views']) {
            throw new Exception("This share has reached its view limit");
        }
        
        $pageId = $shareInfo['page_id'];
    }
    
    // Get page information
    if (!$pageId) {
        throw new Exception("Page ID is required");
    }
    
    $sql = "SELECT * FROM pages WHERE id = ?";
    $stmt = $db->prepare($sql);
    $stmt->execute([$pageId]);
    $page = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$page) {
        throw new Exception("Page not found");
    }
    
    // Prepare form data (exclude system fields)
    $formData = $_POST;
    $systemFields = ['_share_token', '_form_id', '_page_id', '_form_name'];
    foreach ($systemFields as $field) {
        unset($formData[$field]);
    }
    
    if (empty($formData)) {
        throw new Exception("No form data received");
    }
    
    // Get enhanced visitor information
    $ipAddress = getRealIpAddress();
    $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
    $referrer = $_SERVER['HTTP_REFERER'] ?? '';
    $userAgentInfo = parseUserAgent($userAgent);
    $visitorSession = generateVisitorSession($ipAddress, $userAgent);
    
    // Determine submission source
    $submissionSource = 'shared';
    if ($shareInfo === null) {
        $submissionSource = 'direct';
    } elseif (strpos($referrer, $_SERVER['HTTP_HOST']) !== false) {
        $submissionSource = 'direct';
    }
    
    // Store enhanced form submission with better error handling
    try {
        $sql = "INSERT INTO form_submissions (
                    page_id, form_id, share_id, form_name, submission_data,
                    visitor_session, ip_address, user_agent, referrer,
                    browser_name, browser_version, os_name, device_type,
                    submission_source, submitted_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";

        $stmt = $db->prepare($sql);
        $stmt->execute([
            $pageId,
            $formId,
            $shareInfo['id'] ?? null,
            $formName,
            json_encode($formData),
            $visitorSession,
            $ipAddress,
            $userAgent,
            $referrer,
            $userAgentInfo['browser_name'],
            $userAgentInfo['browser_version'],
            $userAgentInfo['os_name'],
            $userAgentInfo['device_type'],
            $submissionSource
        ]);
    } catch (PDOException $e) {
        // If the enhanced columns don't exist, fall back to basic submission
        error_log("Enhanced submission failed, falling back to basic: " . $e->getMessage());

        $sql = "INSERT INTO form_submissions (
                    page_id, form_id, share_id, submission_data,
                    ip_address, user_agent, referrer, submitted_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, NOW())";

        $stmt = $db->prepare($sql);
        $stmt->execute([
            $pageId,
            $formId,
            $shareInfo['id'] ?? null,
            json_encode($formData),
            $ipAddress,
            $userAgent,
            $referrer
        ]);
    }
    
    $submissionId = $db->lastInsertId();
    
    // Log the access with enhanced information
    if ($shareInfo) {
        try {
            // First verify the share exists to prevent foreign key constraint violation
            $sql = "SELECT id FROM page_shares WHERE id = ? AND is_active = 1";
            $stmt = $db->prepare($sql);
            $stmt->execute([$shareInfo['id']]);

            if ($stmt->fetch()) {
                $sql = "INSERT INTO share_access_log (
                            share_id, ip_address, user_agent, referrer,
                            access_type, accessed_at
                        ) VALUES (?, ?, ?, ?, 'form_submission', NOW())";
                $stmt = $db->prepare($sql);
                $stmt->execute([$shareInfo['id'], $ipAddress, $userAgent, $referrer]);

                // Update share view count
                $sql = "UPDATE page_shares SET view_count = view_count + 1 WHERE id = ?";
                $stmt = $db->prepare($sql);
                $stmt->execute([$shareInfo['id']]);
            }
        } catch (PDOException $e) {
            // Log the error but don't break the form submission
            error_log("Failed to log form submission access: " . $e->getMessage());
        }
    }
    
    echo json_encode([
        'success' => true,
        'message' => 'Form submitted successfully',
        'submission_id' => $submissionId,
        'timestamp' => date('Y-m-d H:i:s'),
        'visitor_session' => $visitorSession
    ]);
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage(),
        'timestamp' => date('Y-m-d H:i:s')
    ]);
}
?>
