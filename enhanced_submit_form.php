<?php
/**
 * Enhanced Form Submission Handler
 * Processes form submissions from shared pages with full data collection
 * Works both locally and when deployed online
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

require_once 'config/database.php';

/**
 * Get real IP address (works with proxies and load balancers)
 */
function getRealIpAddress() {
    $ipKeys = ['HTTP_CF_CONNECTING_IP', 'HTTP_X_FORWARDED_FOR', 'HTTP_X_FORWARDED', 
               'HTTP_X_CLUSTER_CLIENT_IP', 'HTTP_FORWARDED_FOR', 'HTTP_FORWARDED', 
               'REMOTE_ADDR'];
    
    foreach ($ipKeys as $key) {
        if (array_key_exists($key, $_SERVER) === true) {
            $ip = $_SERVER[$key];
            if (strpos($ip, ',') !== false) {
                $ip = explode(',', $ip)[0];
            }
            $ip = trim($ip);
            if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                return $ip;
            }
        }
    }
    return $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1';
}

/**
 * Parse user agent for browser and device information
 */
function parseUserAgent($userAgent) {
    $info = [
        'browser_name' => 'Unknown',
        'browser_version' => '',
        'os_name' => 'Unknown',
        'device_type' => 'desktop'
    ];
    
    // Browser detection
    if (preg_match('/Chrome\/([0-9.]+)/', $userAgent, $matches)) {
        $info['browser_name'] = 'Chrome';
        $info['browser_version'] = $matches[1];
    } elseif (preg_match('/Firefox\/([0-9.]+)/', $userAgent, $matches)) {
        $info['browser_name'] = 'Firefox';
        $info['browser_version'] = $matches[1];
    } elseif (preg_match('/Safari\/([0-9.]+)/', $userAgent, $matches)) {
        $info['browser_name'] = 'Safari';
        $info['browser_version'] = $matches[1];
    } elseif (preg_match('/Edge\/([0-9.]+)/', $userAgent, $matches)) {
        $info['browser_name'] = 'Edge';
        $info['browser_version'] = $matches[1];
    }
    
    // OS detection
    if (strpos($userAgent, 'Windows') !== false) {
        $info['os_name'] = 'Windows';
    } elseif (strpos($userAgent, 'Mac') !== false) {
        $info['os_name'] = 'macOS';
    } elseif (strpos($userAgent, 'Linux') !== false) {
        $info['os_name'] = 'Linux';
    } elseif (strpos($userAgent, 'Android') !== false) {
        $info['os_name'] = 'Android';
    } elseif (strpos($userAgent, 'iOS') !== false) {
        $info['os_name'] = 'iOS';
    }
    
    // Device type detection
    if (preg_match('/Mobile|Android|iPhone|iPad/', $userAgent)) {
        if (preg_match('/iPad|Tablet/', $userAgent)) {
            $info['device_type'] = 'tablet';
        } else {
            $info['device_type'] = 'mobile';
        }
    }
    
    return $info;
}

/**
 * Generate visitor session ID
 */
function generateVisitorSession($ip, $userAgent) {
    return hash('sha256', $ip . $userAgent . date('Y-m-d'));
}

try {
    $database = new Database();
    $db = $database->getConnection();
    
    if (!$db) {
        throw new Exception("Database connection failed");
    }
    
    // Get form data
    $shareToken = $_POST['_share_token'] ?? null;
    $formId = $_POST['_form_id'] ?? null;
    $pageId = $_POST['_page_id'] ?? null;
    $formName = $_POST['_form_name'] ?? 'unnamed_form';
    
    if (!$shareToken && !$pageId) {
        throw new Exception("Share token or page ID is required");
    }
    
    // Validate share token and get share info
    $shareInfo = null;
    if ($shareToken) {
        $sql = "SELECT ps.*, p.id as page_id, p.title as page_title 
                FROM page_shares ps 
                JOIN pages p ON ps.page_id = p.id 
                WHERE ps.share_token = ? AND ps.is_active = 1";
        $stmt = $db->prepare($sql);
        $stmt->execute([$shareToken]);
        $shareInfo = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$shareInfo) {
            throw new Exception("Invalid or expired share token");
        }
        
        // Check if forms are enabled for this share
        if (!$shareInfo['show_forms']) {
            throw new Exception("Form submissions are disabled for this share");
        }
        
        // Check expiration
        if ($shareInfo['expires_at'] && strtotime($shareInfo['expires_at']) < time()) {
            throw new Exception("This share has expired");
        }
        
        // Check view limit
        if ($shareInfo['max_views'] && $shareInfo['view_count'] >= $shareInfo['max_views']) {
            throw new Exception("This share has reached its view limit");
        }
        
        $pageId = $shareInfo['page_id'];
    }
    
    // Get page information
    if (!$pageId) {
        throw new Exception("Page ID is required");
    }
    
    $sql = "SELECT * FROM pages WHERE id = ?";
    $stmt = $db->prepare($sql);
    $stmt->execute([$pageId]);
    $page = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$page) {
        throw new Exception("Page not found");
    }
    
    // Extract redirect URL before removing system fields
    $redirectUrl = $_POST['_redirect_url'] ?? null;
    $originalAction = $_POST['_original_action'] ?? null;

    // Prepare form data (exclude system fields)
    $formData = $_POST;
    $systemFields = ['_share_token', '_form_id', '_page_id', '_form_name', '_redirect_url', '_original_action'];
    foreach ($systemFields as $field) {
        unset($formData[$field]);
    }

    // Allow empty form data (some forms might only have hidden fields or be used for tracking)
    // But ensure we have at least the page ID to process the submission
    if (empty($_POST)) {
        throw new Exception("No POST data received");
    }

    if (!$pageId && !$shareToken) {
        throw new Exception("Missing required identifiers (page_id or share_token)");
    }

    // Log form data for debugging (remove in production)
    error_log("Form submission - Page ID: $pageId, Form fields: " . count($formData) . ", System fields removed: " . count($systemFields));
    
    // Get enhanced visitor information
    $ipAddress = getRealIpAddress();
    $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
    $referrer = $_SERVER['HTTP_REFERER'] ?? '';
    $userAgentInfo = parseUserAgent($userAgent);
    $visitorSession = generateVisitorSession($ipAddress, $userAgent);
    
    // Determine submission source
    $submissionSource = 'shared';
    if ($shareInfo === null) {
        $submissionSource = 'direct';
    } elseif (strpos($referrer, $_SERVER['HTTP_HOST']) !== false) {
        $submissionSource = 'direct';
    }
    
    // Store enhanced form submission with automatic column detection
    try {
        // First, check what columns exist in the table
        $sql = "DESCRIBE form_submissions";
        $stmt = $db->prepare($sql);
        $stmt->execute();
        $columns = array_column($stmt->fetchAll(PDO::FETCH_ASSOC), 'Field');

        // Determine which data column to use
        $dataColumn = 'submission_data';
        if (in_array('submission_data', $columns) && !in_array('submission_data', $columns)) {
            $dataColumn = 'submission_data';
        }

        // Build the INSERT query based on available columns
        $insertColumns = ['page_id', $dataColumn];
        $insertValues = [$pageId, json_encode($formData)];
        $placeholders = ['?', '?'];

        // Add optional columns if they exist
        $optionalColumns = [
            'form_id' => $formId,
            'share_id' => $shareInfo['id'] ?? null,
            'form_name' => $formName,
            'visitor_session' => $visitorSession,
            'ip_address' => $ipAddress,
            'user_agent' => $userAgent,
            'referrer' => $referrer,
            'browser_name' => $userAgentInfo['browser_name'],
            'browser_version' => $userAgentInfo['browser_version'],
            'os_name' => $userAgentInfo['os_name'],
            'device_type' => $userAgentInfo['device_type'],
            'submission_source' => $submissionSource
        ];

        foreach ($optionalColumns as $column => $value) {
            if (in_array($column, $columns)) {
                $insertColumns[] = $column;
                $insertValues[] = $value;
                $placeholders[] = '?';
            }
        }

        // Add timestamp column
        if (in_array('submitted_at', $columns)) {
            $insertColumns[] = 'submitted_at';
            $placeholders[] = 'NOW()';
        } elseif (in_array('created_at', $columns)) {
            $insertColumns[] = 'created_at';
            $placeholders[] = 'NOW()';
        }

        $sql = "INSERT INTO form_submissions (" . implode(', ', $insertColumns) . ")
                VALUES (" . implode(', ', $placeholders) . ")";

        $stmt = $db->prepare($sql);
        $stmt->execute($insertValues);

    } catch (PDOException $e) {
        // Ultimate fallback - minimal required columns only
        error_log("Enhanced submission failed, using minimal fallback: " . $e->getMessage());

        // Determine data column again for fallback
        $dataColumn = 'submission_data';
        if (strpos($e->getMessage(), 'submission_data') !== false) {
            $dataColumn = 'submission_data';
        }

        try {
            $sql = "INSERT INTO form_submissions (page_id, $dataColumn, ip_address)
                    VALUES (?, ?, ?)";
            $stmt = $db->prepare($sql);
            $stmt->execute([
                $pageId,
                json_encode($formData),
                $ipAddress
            ]);
        } catch (PDOException $e2) {
            // If even this fails, try with just the essential columns
            $sql = "INSERT INTO form_submissions (page_id, $dataColumn) VALUES (?, ?)";
            $stmt = $db->prepare($sql);
            $stmt->execute([$pageId, json_encode($formData)]);
        }
    }
    
    $submissionId = $db->lastInsertId();
    
    // Log the access with enhanced information
    if ($shareInfo) {
        try {
            // First verify the share exists to prevent foreign key constraint violation
            $sql = "SELECT id FROM page_shares WHERE id = ? AND is_active = 1";
            $stmt = $db->prepare($sql);
            $stmt->execute([$shareInfo['id']]);

            if ($stmt->fetch()) {
                $sql = "INSERT INTO share_access_log (
                            share_id, ip_address, user_agent, referrer,
                            access_type, accessed_at
                        ) VALUES (?, ?, ?, ?, 'form_submission', NOW())";
                $stmt = $db->prepare($sql);
                $stmt->execute([$shareInfo['id'], $ipAddress, $userAgent, $referrer]);

                // Update share view count
                $sql = "UPDATE page_shares SET view_count = view_count + 1 WHERE id = ?";
                $stmt = $db->prepare($sql);
                $stmt->execute([$shareInfo['id']]);
            }
        } catch (PDOException $e) {
            // Log the error but don't break the form submission
            error_log("Failed to log form submission access: " . $e->getMessage());
        }
    }
    
    // Determine final redirect URL
    $finalRedirectUrl = null;

    // Priority order: form-specific redirect > share redirect > original form action
    if (!empty($redirectUrl)) {
        $finalRedirectUrl = $redirectUrl;
    } elseif (!empty($shareInfo['redirect_url'])) {
        $finalRedirectUrl = $shareInfo['redirect_url'];
    } elseif (!empty($originalAction) && filter_var($originalAction, FILTER_VALIDATE_URL)) {
        $finalRedirectUrl = $originalAction;
    }

    echo json_encode([
        'success' => true,
        'message' => 'Form submitted successfully',
        'submission_id' => $submissionId,
        'timestamp' => date('Y-m-d H:i:s'),
        'visitor_session' => $visitorSession,
        'redirect_url' => $finalRedirectUrl
    ]);
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage(),
        'timestamp' => date('Y-m-d H:i:s')
    ]);
}
?>
