<?php
/**
 * Sharing Manager for Webpage Manager
 * Handles page sharing, URL shortening, and access control
 */

// Prevent any output before JSON response
ob_start();

// Set proper headers for JSON response
header('Content-Type: application/json');
header('Cache-Control: no-cache, must-revalidate');

// Suppress PHP warnings/notices that could corrupt JSON
error_reporting(E_ERROR | E_PARSE);

require_once '../config/database.php';

class SharingManager {
    private $db;
    
    public function __construct() {
        $database = new Database();
        $this->db = $database->getConnection();
    }
    
    public function createShare($pageId, $options = []) {
        try {
            // Validate page exists
            $sql = "SELECT * FROM pages WHERE id = ? AND status = 'active'";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$pageId]);
            $page = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$page) {
                return ['success' => false, 'message' => 'Page not found'];
            }
            
            // Generate unique tokens
            $shareToken = $this->generateToken(32);
            $shortCode = $this->generateShortCode();
            
            // Prepare share data
            $title = $options['title'] ?? $page['title'] ?? $page['original_filename'];
            $description = $options['description'] ?? '';
            $password = $options['password'] ?? null;
            $passwordHash = $password ? password_hash($password, PASSWORD_DEFAULT) : null;
            $expiresAt = $options['expires_at'] ?? null;
            $maxViews = $options['max_views'] ?? null;
            $allowDownload = $options['allow_download'] ?? false;
            $showForms = $options['show_forms'] ?? true;
            $showMetadata = $options['show_metadata'] ?? false;
            $redirectUrl = $options['redirect_url'] ?? null;

            // Validate redirect URL if provided
            if ($redirectUrl && !filter_var($redirectUrl, FILTER_VALIDATE_URL)) {
                $redirectUrl = null; // Invalid URL, ignore
            }

            // Insert share record
            $sql = "INSERT INTO page_shares (
                        page_id, share_token, short_code, title, description,
                        password_hash, expires_at, max_views, allow_download,
                        show_forms, show_metadata, redirect_url, created_by
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

            $stmt = $this->db->prepare($sql);
            $stmt->execute([
                $pageId, $shareToken, $shortCode, $title, $description,
                $passwordHash, $expiresAt, $maxViews, $allowDownload,
                $showForms, $showMetadata, $redirectUrl, 1 // Default user ID
            ]);
            
            $shareId = $this->db->lastInsertId();
            
            // Generate URLs
            $baseUrl = $this->getBaseUrl();
            $shareUrl = $baseUrl . '/view.php?token=' . $shareToken;
            $shortUrl = $baseUrl . '/view.php?s=' . $shortCode;
            
            // Log activity
            $this->logActivity('share_created', 'page_share', $shareId, [
                'page_id' => $pageId,
                'short_code' => $shortCode,
                'has_password' => !empty($password),
                'expires_at' => $expiresAt
            ]);
            
            return [
                'success' => true,
                'share' => [
                    'id' => $shareId,
                    'share_token' => $shareToken,
                    'short_code' => $shortCode,
                    'share_url' => $shareUrl,
                    'short_url' => $shortUrl,
                    'title' => $title,
                    'description' => $description,
                    'expires_at' => $expiresAt,
                    'max_views' => $maxViews
                ]
            ];
            
        } catch (PDOException $e) {
            return ['success' => false, 'message' => 'Database error: ' . $e->getMessage()];
        }
    }
    
    public function getShares($pageId = null) {
        try {
            $sql = "SELECT 
                        ps.*,
                        p.title as page_title,
                        p.original_filename,
                        COUNT(sal.id) as total_views
                    FROM page_shares ps
                    JOIN pages p ON ps.page_id = p.id
                    LEFT JOIN share_access_log sal ON ps.id = sal.share_id
                    WHERE ps.is_active = 1";
            
            $params = [];
            if ($pageId) {
                $sql .= " AND ps.page_id = ?";
                $params[] = $pageId;
            }
            
            $sql .= " GROUP BY ps.id ORDER BY ps.created_at DESC";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);
            $shares = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // Add URLs and status info
            $baseUrl = $this->getBaseUrl();
            foreach ($shares as &$share) {
                $share['share_url'] = $baseUrl . '/view.php?token=' . $share['share_token'];
                $share['short_url'] = $baseUrl . '/view.php?s=' . $share['short_code'];
                $share['is_expired'] = $share['expires_at'] && strtotime($share['expires_at']) < time();
                $share['is_view_limit_reached'] = $share['max_views'] && $share['view_count'] >= $share['max_views'];
                $share['has_password'] = !empty($share['password_hash']);
            }
            
            return ['success' => true, 'shares' => $shares];

        } catch (PDOException $e) {
            return ['success' => false, 'message' => 'Database error: ' . $e->getMessage()];
        }
    }

    public function getShare($shareId) {
        try {
            $sql = "SELECT
                        ps.*,
                        p.title as page_title,
                        p.original_filename,
                        COUNT(sal.id) as total_views
                    FROM page_shares ps
                    JOIN pages p ON ps.page_id = p.id
                    LEFT JOIN share_access_log sal ON ps.id = sal.share_id
                    WHERE ps.id = ? AND ps.is_active = 1
                    GROUP BY ps.id";

            $stmt = $this->db->prepare($sql);
            $stmt->execute([$shareId]);
            $share = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$share) {
                return ['success' => false, 'message' => 'Share not found'];
            }

            // Add URLs and status info
            $baseUrl = $this->getBaseUrl();
            $share['share_url'] = $baseUrl . '/view.php?token=' . $share['share_token'];
            $share['short_url'] = $baseUrl . '/view.php?s=' . $share['short_code'];
            $share['is_expired'] = $share['expires_at'] && strtotime($share['expires_at']) < time();
            $share['is_view_limit_reached'] = $share['max_views'] && $share['view_count'] >= $share['max_views'];
            $share['has_password'] = !empty($share['password_hash']);

            return ['success' => true, 'share' => $share];
        } catch (PDOException $e) {
            return ['success' => false, 'message' => 'Database error: ' . $e->getMessage()];
        }
    }

    public function updateShare($shareId, $updates) {
        try {
            $allowedFields = [
                'title', 'description', 'expires_at', 'max_views',
                'allow_download', 'show_forms', 'show_metadata', 'is_active', 'redirect_url'
            ];
            
            $setParts = [];
            $values = [];
            
            foreach ($updates as $field => $value) {
                if (in_array($field, $allowedFields)) {
                    $setParts[] = "$field = ?";
                    $values[] = $value;
                }
            }
            
            if (empty($setParts)) {
                return ['success' => false, 'message' => 'No valid fields to update'];
            }
            
            // Handle password update separately
            if (isset($updates['password'])) {
                if ($updates['password']) {
                    $setParts[] = "password_hash = ?";
                    $values[] = password_hash($updates['password'], PASSWORD_DEFAULT);
                } else {
                    $setParts[] = "password_hash = NULL";
                }
            }
            
            $values[] = $shareId;
            $sql = "UPDATE page_shares SET " . implode(', ', $setParts) . " WHERE id = ?";
            
            $stmt = $this->db->prepare($sql);
            $result = $stmt->execute($values);
            
            if ($result) {
                $this->logActivity('share_updated', 'page_share', $shareId, $updates);
                return ['success' => true, 'message' => 'Share updated successfully'];
            } else {
                return ['success' => false, 'message' => 'Failed to update share'];
            }
            
        } catch (PDOException $e) {
            return ['success' => false, 'message' => 'Database error: ' . $e->getMessage()];
        }
    }
    
    public function deleteShare($shareId) {
        try {
            $sql = "UPDATE page_shares SET is_active = 0 WHERE id = ?";
            $stmt = $this->db->prepare($sql);
            $result = $stmt->execute([$shareId]);
            
            if ($result) {
                $this->logActivity('share_deleted', 'page_share', $shareId);
                return ['success' => true, 'message' => 'Share deleted successfully'];
            } else {
                return ['success' => false, 'message' => 'Failed to delete share'];
            }
            
        } catch (PDOException $e) {
            return ['success' => false, 'message' => 'Database error: ' . $e->getMessage()];
        }
    }
    
    public function getShareAnalytics($shareId) {
        try {
            // Get share info
            $sql = "SELECT * FROM page_shares WHERE id = ?";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$shareId]);
            $share = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$share) {
                return ['success' => false, 'message' => 'Share not found'];
            }
            
            // Get access statistics
            $sql = "SELECT 
                        COUNT(*) as total_views,
                        COUNT(DISTINCT ip_address) as unique_visitors,
                        DATE(accessed_at) as date,
                        COUNT(*) as daily_views
                    FROM share_access_log 
                    WHERE share_id = ? 
                    GROUP BY DATE(accessed_at)
                    ORDER BY date DESC
                    LIMIT 30";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$shareId]);
            $dailyStats = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // Get recent access log
            $sql = "SELECT * FROM share_access_log 
                    WHERE share_id = ? 
                    ORDER BY accessed_at DESC 
                    LIMIT 50";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$shareId]);
            $recentAccess = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            return [
                'success' => true,
                'analytics' => [
                    'share' => $share,
                    'daily_stats' => $dailyStats,
                    'recent_access' => $recentAccess
                ]
            ];
            
        } catch (PDOException $e) {
            return ['success' => false, 'message' => 'Database error: ' . $e->getMessage()];
        }
    }
    
    public function createShortUrl($originalUrl, $options = []) {
        try {
            $shortCode = $this->generateShortCode();
            $title = $options['title'] ?? '';
            $description = $options['description'] ?? '';
            $expiresAt = $options['expires_at'] ?? null;
            
            $sql = "INSERT INTO short_urls (original_url, short_code, title, description, expires_at, created_by) 
                    VALUES (?, ?, ?, ?, ?, ?)";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$originalUrl, $shortCode, $title, $description, $expiresAt, 1]);
            
            $shortUrlId = $this->db->lastInsertId();
            $baseUrl = $this->getBaseUrl();
            $shortUrl = $baseUrl . '/s/' . $shortCode;
            
            return [
                'success' => true,
                'short_url' => [
                    'id' => $shortUrlId,
                    'short_code' => $shortCode,
                    'short_url' => $shortUrl,
                    'original_url' => $originalUrl
                ]
            ];
            
        } catch (PDOException $e) {
            return ['success' => false, 'message' => 'Database error: ' . $e->getMessage()];
        }
    }
    
    private function generateToken($length = 32) {
        return bin2hex(random_bytes($length / 2));
    }
    
    private function generateShortCode($length = 6) {
        $characters = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        $code = '';
        
        do {
            $code = '';
            for ($i = 0; $i < $length; $i++) {
                $code .= $characters[random_int(0, strlen($characters) - 1)];
            }
            
            // Check if code already exists
            $sql = "SELECT id FROM page_shares WHERE short_code = ? 
                    UNION 
                    SELECT id FROM short_urls WHERE short_code = ?";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$code, $code]);
            $exists = $stmt->fetch();
            
        } while ($exists);
        
        return $code;
    }
    
    private function getBaseUrl() {
        $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
        $host = $_SERVER['HTTP_HOST'] ?? 'localhost';

        // Get the correct base path - remove /includes/ if present
        $scriptPath = $_SERVER['SCRIPT_NAME'] ?? '';
        $basePath = dirname($scriptPath);

        // If we're in the includes directory, go up one level
        if (basename($basePath) === 'includes') {
            $basePath = dirname($basePath);
        }

        return $protocol . '://' . $host . rtrim($basePath, '/');
    }
    
    private function logActivity($action, $entityType, $entityId, $data = []) {
        try {
            $sql = "INSERT INTO activity_log (user_id, action, entity_type, entity_id, new_values, ip_address, user_agent) 
                    VALUES (?, ?, ?, ?, ?, ?, ?)";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute([
                1, // Default user ID
                $action,
                $entityType,
                $entityId,
                json_encode($data),
                $_SERVER['REMOTE_ADDR'] ?? '',
                $_SERVER['HTTP_USER_AGENT'] ?? ''
            ]);
        } catch (PDOException $e) {
            // Log errors silently
            error_log("Activity logging failed: " . $e->getMessage());
        }
    }

    /**
     * Get form submissions for a page or share
     */
    public function getFormSubmissions($pageId = null, $shareId = null) {
        try {
            // Detect which data column exists
            $sql = "DESCRIBE form_submissions";
            $stmt = $this->db->prepare($sql);
            $stmt->execute();
            $columns = array_column($stmt->fetchAll(PDO::FETCH_ASSOC), 'Field');

            $dataColumn = 'submission_data';
            if (in_array('form_data', $columns) && !in_array('submission_data', $columns)) {
                $dataColumn = 'form_data';
            }

            // Build WHERE clause
            $whereConditions = [];
            $params = [];

            if ($pageId) {
                $whereConditions[] = "fs.page_id = ?";
                $params[] = $pageId;
            }

            if ($shareId) {
                $whereConditions[] = "fs.share_id = ?";
                $params[] = $shareId;
            }

            $whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';

            // Get submissions with related data
            $sql = "SELECT
                        fs.id,
                        fs.page_id,
                        fs.form_id,
                        fs.share_id,
                        fs.$dataColumn as form_data,
                        fs.ip_address,
                        fs.user_agent,
                        fs.referrer,
                        fs.submitted_at,
                        p.title as page_title,
                        p.original_filename as page_filename,
                        f.form_name,
                        ps.title as share_title
                    FROM form_submissions fs
                    LEFT JOIN pages p ON fs.page_id = p.id
                    LEFT JOIN forms f ON fs.form_id = f.id
                    LEFT JOIN page_shares ps ON fs.share_id = ps.id
                    $whereClause
                    ORDER BY fs.submitted_at DESC
                    LIMIT 100";

            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);
            $submissions = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // Get total count
            $countSql = "SELECT COUNT(*) FROM form_submissions fs $whereClause";
            $stmt = $this->db->prepare($countSql);
            $stmt->execute($params);
            $total = $stmt->fetchColumn();

            return [
                'success' => true,
                'submissions' => $submissions,
                'total' => $total
            ];

        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'Failed to get submissions: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Get a specific form submission
     */
    public function getFormSubmission($submissionId) {
        try {
            if (!$submissionId) {
                throw new Exception("Submission ID is required");
            }

            // Detect which data column exists
            $sql = "DESCRIBE form_submissions";
            $stmt = $this->db->prepare($sql);
            $stmt->execute();
            $columns = array_column($stmt->fetchAll(PDO::FETCH_ASSOC), 'Field');

            $dataColumn = 'submission_data';
            if (in_array('form_data', $columns) && !in_array('submission_data', $columns)) {
                $dataColumn = 'form_data';
            }

            $sql = "SELECT
                        fs.*,
                        fs.$dataColumn as form_data,
                        p.title as page_title,
                        p.original_filename as page_filename,
                        f.form_name,
                        ps.title as share_title
                    FROM form_submissions fs
                    LEFT JOIN pages p ON fs.page_id = p.id
                    LEFT JOIN forms f ON fs.form_id = f.id
                    LEFT JOIN page_shares ps ON fs.share_id = ps.id
                    WHERE fs.id = ?";

            $stmt = $this->db->prepare($sql);
            $stmt->execute([$submissionId]);
            $submission = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$submission) {
                throw new Exception("Submission not found");
            }

            return [
                'success' => true,
                'submission' => $submission
            ];

        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'Failed to get submission: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Delete a form submission
     */
    public function deleteFormSubmission($submissionId) {
        try {
            if (!$submissionId) {
                throw new Exception("Submission ID is required");
            }

            // Check if submission exists
            $sql = "SELECT id, page_id FROM form_submissions WHERE id = ?";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$submissionId]);
            $submission = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$submission) {
                throw new Exception("Submission not found");
            }

            // Delete the submission
            $sql = "DELETE FROM form_submissions WHERE id = ?";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$submissionId]);

            return [
                'success' => true,
                'message' => 'Submission deleted successfully'
            ];

        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'Failed to delete submission: ' . $e->getMessage()
            ];
        }
    }
}

// Handle the request
try {
    $manager = new SharingManager();
    $action = $_POST['action'] ?? $_GET['action'] ?? '';
    
    switch ($action) {
        case 'create_share':
            $pageId = $_POST['page_id'] ?? 0;
            $options = $_POST['options'] ?? [];
            $result = $manager->createShare($pageId, $options);
            break;
            
        case 'get_shares':
            $pageId = $_GET['page_id'] ?? null;
            $result = $manager->getShares($pageId);
            break;

        case 'get_share':
            $shareId = $_GET['share_id'] ?? 0;
            $result = $manager->getShare($shareId);
            break;

        case 'update_share':
            $shareId = $_POST['share_id'] ?? 0;
            $updates = $_POST['updates'] ?? [];
            $result = $manager->updateShare($shareId, $updates);
            break;
            
        case 'delete_share':
            $shareId = $_POST['share_id'] ?? 0;
            $result = $manager->deleteShare($shareId);
            break;
            
        case 'get_analytics':
            $shareId = $_GET['share_id'] ?? 0;
            $result = $manager->getShareAnalytics($shareId);
            break;
            
        case 'create_short_url':
            $originalUrl = $_POST['original_url'] ?? '';
            $options = $_POST['options'] ?? [];
            $result = $manager->createShortUrl($originalUrl, $options);
            break;
            
        case 'get_submissions':
            $pageId = $_POST['page_id'] ?? $_GET['page_id'] ?? null;
            $shareId = $_POST['share_id'] ?? $_GET['share_id'] ?? null;
            $result = $this->getFormSubmissions($pageId, $shareId);
            break;

        case 'delete_submission':
            $submissionId = $_POST['submission_id'] ?? null;
            $result = $this->deleteFormSubmission($submissionId);
            break;

        case 'get_submission':
            $submissionId = $_POST['submission_id'] ?? $_GET['submission_id'] ?? null;
            $result = $this->getFormSubmission($submissionId);
            break;

        default:
            $result = ['success' => false, 'message' => 'Invalid action'];
    }

    // Clean any unexpected output
    ob_clean();

    echo json_encode($result);

} catch (Exception $e) {
    // Clean any unexpected output
    ob_clean();

    echo json_encode([
        'success' => false,
        'message' => 'Server error: ' . $e->getMessage()
    ]);
}
