<?php
/**
 * Enhanced Upload Handler for Webpage Manager v2.0
 * Supports individual page imports with associated files and ZIP uploads
 */

require_once '../config/database.php';
require_once 'html_parser.php';

header('Content-Type: application/json');

class EnhancedUploadHandler {
    private $db;
    private $uploadDir;
    private $maxFileSize;
    private $allowedTypes;
    
    public function __construct() {
        $database = new Database();
        $this->db = $database->getConnection();
        
        // Use absolute path for uploads directory
        $this->uploadDir = dirname(__DIR__) . DIRECTORY_SEPARATOR . 'uploads' . DIRECTORY_SEPARATOR;
        $this->maxFileSize = 50 * 1024 * 1024; // 50MB for ZIP files
        
        // Create upload directories if they don't exist
        $this->createUploadDirectories();
        
        $this->allowedTypes = [
            'html' => ['text/html', 'application/xhtml+xml'],
            'css' => ['text/css'],
            'js' => ['application/javascript', 'text/javascript'],
            'image' => ['image/jpeg', 'image/png', 'image/gif', 'image/svg+xml', 'image/webp', 'image/bmp', 'image/tiff'],
            'font' => ['font/woff', 'font/woff2', 'font/ttf', 'font/otf', 'application/font-woff', 'application/font-woff2'],
            'archive' => ['application/zip', 'application/x-zip-compressed'],
            'document' => ['application/pdf', 'text/plain']
        ];
    }
    
    private function createUploadDirectories() {
        $dirs = ['pages', 'assets', 'temp', 'projects'];
        foreach ($dirs as $dir) {
            $path = $this->uploadDir . $dir;
            if (!is_dir($path)) {
                mkdir($path, 0755, true);
            }
        }
    }
    
    public function handleUpload() {
        try {
            if (!isset($_FILES['files'])) {
                return ['success' => false, 'message' => 'No files uploaded'];
            }
            
            $uploadType = $_POST['upload_type'] ?? 'individual';
            $projectId = $_POST['project_id'] ?? 1;
            
            $files = $_FILES['files'];
            $results = [];
            
            // Handle both single and multiple file uploads
            if (!is_array($files['name'])) {
                $files = [
                    'name' => [$files['name']],
                    'tmp_name' => [$files['tmp_name']],
                    'size' => [$files['size']],
                    'type' => [$files['type']],
                    'error' => [$files['error']]
                ];
            }
            
            // Process each file
            for ($i = 0; $i < count($files['name']); $i++) {
                if ($files['error'][$i] === UPLOAD_ERR_OK) {
                    $file = [
                        'name' => $files['name'][$i],
                        'tmp_name' => $files['tmp_name'][$i],
                        'size' => $files['size'][$i],
                        'type' => $files['type'][$i]
                    ];
                    
                    // Check if it's a ZIP file
                    $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
                    if ($extension === 'zip') {
                        $result = $this->processZipFile($file, $projectId);
                    } else {
                        $result = $this->processIndividualFile($file, $projectId, $uploadType);
                    }
                    
                    $results[] = $result;
                } else {
                    $results[] = [
                        'success' => false,
                        'name' => $files['name'][$i],
                        'message' => $this->getUploadError($files['error'][$i])
                    ];
                }
            }
            
            return [
                'success' => true,
                'results' => $results,
                'total_files' => count($results),
                'successful_uploads' => count(array_filter($results, function($r) { return $r['success']; }))
            ];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'Upload error: ' . $e->getMessage()];
        }
    }
    
    private function processZipFile($file, $projectId) {
        try {
            // Create temporary extraction directory
            $tempDir = $this->uploadDir . 'temp' . DIRECTORY_SEPARATOR . uniqid('zip_') . DIRECTORY_SEPARATOR;
            mkdir($tempDir, 0755, true);
            
            // Extract ZIP file
            $zip = new ZipArchive();
            $result = $zip->open($file['tmp_name']);
            
            if ($result !== TRUE) {
                return ['success' => false, 'name' => $file['name'], 'message' => 'Failed to open ZIP file: ' . $this->getZipError($result)];
            }
            
            $zip->extractTo($tempDir);
            $zip->close();
            
            // Find HTML files and their associated assets
            $htmlFiles = $this->findHtmlFiles($tempDir);
            $processedPages = [];
            
            foreach ($htmlFiles as $htmlFile) {
                $pageResult = $this->processHtmlWithAssets($htmlFile, $tempDir, $projectId);
                if ($pageResult['success']) {
                    $processedPages[] = $pageResult;
                }
            }
            
            // Clean up temporary directory
            $this->removeDirectory($tempDir);
            
            return [
                'success' => true,
                'name' => $file['name'],
                'type' => 'zip',
                'message' => 'ZIP file processed successfully',
                'pages_created' => count($processedPages),
                'pages' => $processedPages
            ];
            
        } catch (Exception $e) {
            return ['success' => false, 'name' => $file['name'], 'message' => 'ZIP processing error: ' . $e->getMessage()];
        }
    }
    
    private function processIndividualFile($file, $projectId, $uploadType) {
        // Validate file
        $validation = $this->validateFile($file);
        if (!$validation['valid']) {
            return ['success' => false, 'name' => $file['name'], 'message' => $validation['message']];
        }
        
        // Generate unique filename
        $fileInfo = pathinfo($file['name']);
        $extension = strtolower($fileInfo['extension']);
        $baseName = $fileInfo['filename'];
        $uniqueName = $baseName . '_' . time() . '_' . uniqid() . '.' . $extension;
        
        // Determine file type category
        $fileType = $this->getFileTypeCategory($file['type'], $extension);
        
        // Create directory structure
        $targetDir = $this->uploadDir . ($fileType === 'html' ? 'pages' : 'assets') . DIRECTORY_SEPARATOR;
        $targetPath = $targetDir . $uniqueName;
        
        // Move uploaded file
        if (move_uploaded_file($file['tmp_name'], $targetPath)) {
            // Save to database
            $pageId = $this->saveToDatabase($file, $uniqueName, $targetPath, $fileType, $projectId);
            
            if ($pageId) {
                // If it's an HTML file, analyze it for forms and extract metadata
                if ($fileType === 'html') {
                    $this->analyzeHTMLFile($pageId, $targetPath);
                    $this->extractAndSaveMetadata($pageId, $targetPath);
                }
                
                return [
                    'success' => true,
                    'name' => $file['name'],
                    'message' => 'Uploaded successfully',
                    'file_type' => $fileType,
                    'page_id' => $pageId,
                    'file_path' => $targetPath,
                    'unique_name' => $uniqueName
                ];
            } else {
                unlink($targetPath);
                return ['success' => false, 'name' => $file['name'], 'message' => 'Failed to save file information to database'];
            }
        } else {
            return ['success' => false, 'name' => $file['name'], 'message' => 'Failed to move uploaded file'];
        }
    }
    
    private function findHtmlFiles($directory) {
        $htmlFiles = [];
        $iterator = new RecursiveIteratorIterator(new RecursiveDirectoryIterator($directory));
        
        foreach ($iterator as $file) {
            if ($file->isFile()) {
                $extension = strtolower($file->getExtension());
                if (in_array($extension, ['html', 'htm'])) {
                    $htmlFiles[] = $file->getPathname();
                }
            }
        }
        
        return $htmlFiles;
    }
    
    private function processHtmlWithAssets($htmlFilePath, $tempDir, $projectId) {
        try {
            // Read HTML content to find referenced assets
            $htmlContent = file_get_contents($htmlFilePath);
            $referencedAssets = $this->extractAssetReferences($htmlContent);
            
            // Create page entry
            $pageInfo = [
                'name' => basename($htmlFilePath),
                'tmp_name' => $htmlFilePath,
                'size' => filesize($htmlFilePath),
                'type' => 'text/html'
            ];
            
            $pageResult = $this->processIndividualFile($pageInfo, $projectId, 'zip');
            
            if (!$pageResult['success']) {
                return $pageResult;
            }
            
            $pageId = $pageResult['page_id'];
            $associatedFiles = [];
            
            // Process referenced assets
            foreach ($referencedAssets as $assetPath) {
                $fullAssetPath = $this->resolveAssetPath($assetPath, $htmlFilePath, $tempDir);
                
                if ($fullAssetPath && file_exists($fullAssetPath)) {
                    $assetInfo = [
                        'name' => basename($fullAssetPath),
                        'tmp_name' => $fullAssetPath,
                        'size' => filesize($fullAssetPath),
                        'type' => mime_content_type($fullAssetPath)
                    ];
                    
                    $assetResult = $this->processAssetFile($assetInfo, $pageId);
                    if ($assetResult['success']) {
                        $associatedFiles[] = $assetResult;
                    }
                }
            }
            
            return [
                'success' => true,
                'name' => basename($htmlFilePath),
                'page_id' => $pageId,
                'associated_files' => count($associatedFiles),
                'assets' => $associatedFiles
            ];
            
        } catch (Exception $e) {
            return ['success' => false, 'name' => basename($htmlFilePath), 'message' => 'HTML processing error: ' . $e->getMessage()];
        }
    }
    
    private function extractAssetReferences($htmlContent) {
        $assets = [];
        
        // Extract CSS links
        if (preg_match_all('/<link[^>]+href=["\']([^"\']+)["\'][^>]*>/i', $htmlContent, $matches)) {
            $assets = array_merge($assets, $matches[1]);
        }
        
        // Extract script sources
        if (preg_match_all('/<script[^>]+src=["\']([^"\']+)["\'][^>]*>/i', $htmlContent, $matches)) {
            $assets = array_merge($assets, $matches[1]);
        }
        
        // Extract image sources
        if (preg_match_all('/<img[^>]+src=["\']([^"\']+)["\'][^>]*>/i', $htmlContent, $matches)) {
            $assets = array_merge($assets, $matches[1]);
        }
        
        // Extract CSS background images
        if (preg_match_all('/background-image:\s*url\(["\']?([^"\')\s]+)["\']?\)/i', $htmlContent, $matches)) {
            $assets = array_merge($assets, $matches[1]);
        }
        
        // Filter out external URLs and data URIs
        $assets = array_filter($assets, function($asset) {
            return !preg_match('/^(https?:|data:|\/\/)/i', $asset);
        });
        
        return array_unique($assets);
    }
    
    private function resolveAssetPath($assetPath, $htmlFilePath, $tempDir) {
        $htmlDir = dirname($htmlFilePath);
        
        // Handle relative paths
        if (strpos($assetPath, '/') === 0) {
            // Absolute path from root
            $fullPath = $tempDir . $assetPath;
        } else {
            // Relative path
            $fullPath = $htmlDir . DIRECTORY_SEPARATOR . $assetPath;
        }
        
        // Normalize path
        $fullPath = realpath($fullPath);
        
        // Ensure the path is within the temp directory
        if ($fullPath && strpos($fullPath, realpath($tempDir)) === 0) {
            return $fullPath;
        }
        
        return null;
    }
    
    private function processAssetFile($file, $pageId) {
        // Generate unique filename
        $fileInfo = pathinfo($file['name']);
        $extension = strtolower($fileInfo['extension']);
        $baseName = $fileInfo['filename'];
        $uniqueName = $baseName . '_' . time() . '_' . uniqid() . '.' . $extension;
        
        // Determine file type
        $fileType = $this->getFileTypeCategory($file['type'], $extension);
        
        // Target directory
        $targetDir = $this->uploadDir . 'assets' . DIRECTORY_SEPARATOR;
        $targetPath = $targetDir . $uniqueName;
        
        // Copy file (since it's from temp directory)
        if (copy($file['tmp_name'], $targetPath)) {
            // Save to database as associated file
            $sql = "INSERT INTO associated_files (page_id, filename, original_filename, file_path, file_type, file_size, mime_type, is_referenced) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, 1)";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([
                $pageId,
                $uniqueName,
                $file['name'],
                $targetPath,
                $fileType,
                $file['size'],
                $file['type']
            ]);
            
            return [
                'success' => true,
                'name' => $file['name'],
                'unique_name' => $uniqueName,
                'file_type' => $fileType
            ];
        }
        
        return ['success' => false, 'name' => $file['name'], 'message' => 'Failed to copy asset file'];
    }

    private function validateFile($file) {
        // Check file size
        if ($file['size'] > $this->maxFileSize) {
            return ['valid' => false, 'message' => 'File too large. Maximum size: ' . $this->formatFileSize($this->maxFileSize)];
        }

        // Check file type
        $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        $mimeType = $file['type'];

        $isValidType = false;
        foreach ($this->allowedTypes as $category => $types) {
            if (in_array($mimeType, $types)) {
                $isValidType = true;
                break;
            }
        }

        // Additional extension check for common files
        $allowedExtensions = ['html', 'htm', 'css', 'js', 'png', 'jpg', 'jpeg', 'gif', 'svg', 'webp', 'woff', 'woff2', 'ttf', 'otf', 'zip', 'pdf', 'txt'];
        if (!$isValidType && !in_array($extension, $allowedExtensions)) {
            return ['valid' => false, 'message' => 'File type not allowed: ' . $extension];
        }

        return ['valid' => true];
    }

    private function getFileTypeCategory($mimeType, $extension) {
        foreach ($this->allowedTypes as $category => $types) {
            if (in_array($mimeType, $types)) {
                return $category;
            }
        }

        // Fallback based on extension
        $extensionMap = [
            'html' => 'html', 'htm' => 'html',
            'css' => 'css',
            'js' => 'js',
            'png' => 'image', 'jpg' => 'image', 'jpeg' => 'image', 'gif' => 'image', 'svg' => 'image', 'webp' => 'image',
            'woff' => 'font', 'woff2' => 'font', 'ttf' => 'font', 'otf' => 'font',
            'zip' => 'archive',
            'pdf' => 'document', 'txt' => 'document'
        ];

        return $extensionMap[$extension] ?? 'unknown';
    }

    private function saveToDatabase($file, $uniqueName, $targetPath, $fileType, $projectId) {
        try {
            if ($fileType === 'html') {
                // Save as main page
                $title = $this->extractTitle($targetPath);

                $sql = "INSERT INTO pages (project_id, filename, original_filename, title, file_path, file_size, file_hash, content_type, status)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'active')";
                $stmt = $this->db->prepare($sql);
                $stmt->execute([
                    $projectId,
                    $uniqueName,
                    $file['name'],
                    $title,
                    $targetPath,
                    $file['size'],
                    hash_file('sha256', $targetPath),
                    $file['type']
                ]);

                return $this->db->lastInsertId();
            } else {
                // Save as associated file (orphaned for now, will be linked later)
                $sql = "INSERT INTO associated_files (page_id, filename, original_filename, file_path, file_type, file_size, mime_type, is_referenced)
                        VALUES (0, ?, ?, ?, ?, ?, ?, 0)";
                $stmt = $this->db->prepare($sql);
                $stmt->execute([
                    $uniqueName,
                    $file['name'],
                    $targetPath,
                    $fileType,
                    $file['size'],
                    $file['type']
                ]);

                return $this->db->lastInsertId();
            }
        } catch (PDOException $e) {
            error_log("Database error: " . $e->getMessage());
            return false;
        }
    }

    private function extractTitle($filePath) {
        $content = file_get_contents($filePath);
        if (preg_match('/<title[^>]*>(.*?)<\/title>/is', $content, $matches)) {
            return trim(strip_tags($matches[1]));
        }
        return null;
    }

    private function extractAndSaveMetadata($pageId, $filePath) {
        try {
            $parser = new HTMLParser();
            $metadata = $parser->extractMetadata($filePath);

            // Update page with metadata
            $sql = "UPDATE pages SET
                        meta_description = ?,
                        meta_keywords = ?,
                        language = ?,
                        charset = ?
                    WHERE id = ?";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([
                $metadata['description'],
                $metadata['keywords'],
                $metadata['language'] ?: 'en',
                $metadata['charset'] ?: 'UTF-8',
                $pageId
            ]);

        } catch (Exception $e) {
            error_log("Metadata extraction error: " . $e->getMessage());
        }
    }

    private function analyzeHTMLFile($pageId, $filePath) {
        try {
            $parser = new HTMLParser();
            $forms = $parser->extractForms($filePath);

            $totalFields = 0;
            $formTypes = [];

            foreach ($forms as $index => $formData) {
                // Save form with enhanced attributes
                $sql = "INSERT INTO forms (page_id, form_name, form_action, form_method, form_enctype, form_id, form_class, form_target, form_autocomplete, form_novalidate, form_index, form_description, is_active, validation_rules, custom_attributes)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 1, ?, ?)";
                $stmt = $this->db->prepare($sql);
                $stmt->execute([
                    $pageId,
                    $formData['name'],
                    $formData['action'],
                    $formData['method'],
                    $formData['enctype'],
                    $formData['id'],
                    $formData['class'],
                    $formData['target'] ?? null,
                    $formData['autocomplete'] ?? 'on',
                    $formData['novalidate'] ?? false,
                    $formData['index'] ?? $index,
                    $formData['description'] ?? null,
                    json_encode($formData['validation_rules'] ?? []),
                    json_encode($formData['custom_attributes'] ?? [])
                ]);

                $formId = $this->db->lastInsertId();
                $formTypes[] = $formData['type'] ?? 'form';

                // Save form fields with enhanced attributes
                foreach ($formData['fields'] as $fieldOrder => $fieldData) {
                    $sql = "INSERT INTO form_fields (
                        form_id, field_name, field_type, field_id, field_class, field_placeholder, field_value, field_label,
                        field_required, field_readonly, field_disabled, field_multiple, field_min, field_max, field_step,
                        field_pattern, field_maxlength, field_minlength, field_size, field_rows, field_cols, field_accept,
                        field_autocomplete, field_options, field_validation_rules, field_custom_attributes, field_order, is_active
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 1)";

                    $stmt = $this->db->prepare($sql);
                    $stmt->execute([
                        $formId,
                        $fieldData['name'],
                        $fieldData['type'],
                        $fieldData['id'],
                        $fieldData['class'],
                        $fieldData['placeholder'],
                        $fieldData['value'],
                        $fieldData['label'],
                        $fieldData['required'] ? 1 : 0,
                        $fieldData['readonly'] ? 1 : 0,
                        $fieldData['disabled'] ? 1 : 0,
                        $fieldData['multiple'] ? 1 : 0,
                        $fieldData['min'] ?? null,
                        $fieldData['max'] ?? null,
                        $fieldData['step'] ?? null,
                        $fieldData['pattern'] ?? null,
                        $fieldData['maxlength'] ?? null,
                        $fieldData['minlength'] ?? null,
                        $fieldData['size'] ?? null,
                        $fieldData['rows'] ?? null,
                        $fieldData['cols'] ?? null,
                        $fieldData['accept'] ?? null,
                        $fieldData['autocomplete'] ?? null,
                        json_encode($fieldData['options'] ?? []),
                        json_encode($fieldData['validation'] ?? []),
                        json_encode($fieldData['data_attributes'] ?? []),
                        $fieldOrder
                    ]);

                    $totalFields++;
                }
            }

            // Enhanced logging with detailed analysis
            $analysisDetails = [
                'forms_count' => count($forms),
                'total_fields' => $totalFields,
                'form_types' => array_count_values($formTypes),
                'timestamp' => date('Y-m-d H:i:s'),
                'features_detected' => [
                    'standalone_inputs' => in_array('standalone', $formTypes),
                    'custom_forms' => in_array('custom', $formTypes),
                    'interactive_elements' => in_array('interactive', $formTypes),
                    'traditional_forms' => in_array('form', $formTypes)
                ]
            ];

            $sql = "INSERT INTO analysis_log (page_id, analysis_type, status, message, details)
                    VALUES (?, 'enhanced_form_analysis', 'success', ?, ?)";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([
                $pageId,
                "Found {$totalFields} input elements in " . count($forms) . " form structures",
                json_encode($analysisDetails)
            ]);

        } catch (Exception $e) {
            // Log error
            $sql = "INSERT INTO analysis_log (page_id, analysis_type, status, message)
                    VALUES (?, 'enhanced_form_analysis', 'error', ?)";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$pageId, $e->getMessage()]);

            error_log("Enhanced form analysis error: " . $e->getMessage());
        }
    }

    private function removeDirectory($dir) {
        if (!is_dir($dir)) return;

        $files = array_diff(scandir($dir), ['.', '..']);
        foreach ($files as $file) {
            $path = $dir . DIRECTORY_SEPARATOR . $file;
            is_dir($path) ? $this->removeDirectory($path) : unlink($path);
        }
        rmdir($dir);
    }

    private function getUploadError($error) {
        $errors = [
            UPLOAD_ERR_INI_SIZE => 'File exceeds upload_max_filesize',
            UPLOAD_ERR_FORM_SIZE => 'File exceeds MAX_FILE_SIZE',
            UPLOAD_ERR_PARTIAL => 'File was only partially uploaded',
            UPLOAD_ERR_NO_FILE => 'No file was uploaded',
            UPLOAD_ERR_NO_TMP_DIR => 'Missing temporary folder',
            UPLOAD_ERR_CANT_WRITE => 'Failed to write file to disk',
            UPLOAD_ERR_EXTENSION => 'File upload stopped by extension'
        ];

        return $errors[$error] ?? 'Unknown upload error';
    }

    private function getZipError($error) {
        $errors = [
            ZipArchive::ER_OK => 'No error',
            ZipArchive::ER_MULTIDISK => 'Multi-disk zip archives not supported',
            ZipArchive::ER_RENAME => 'Renaming temporary file failed',
            ZipArchive::ER_CLOSE => 'Closing zip archive failed',
            ZipArchive::ER_SEEK => 'Seek error',
            ZipArchive::ER_READ => 'Read error',
            ZipArchive::ER_WRITE => 'Write error',
            ZipArchive::ER_CRC => 'CRC error',
            ZipArchive::ER_ZIPCLOSED => 'Containing zip archive was closed',
            ZipArchive::ER_NOENT => 'No such file',
            ZipArchive::ER_EXISTS => 'File already exists',
            ZipArchive::ER_OPEN => 'Can not open file',
            ZipArchive::ER_TMPOPEN => 'Failure to create temporary file',
            ZipArchive::ER_ZLIB => 'Zlib error',
            ZipArchive::ER_MEMORY => 'Memory allocation failure',
            ZipArchive::ER_CHANGED => 'Entry has been changed',
            ZipArchive::ER_COMPNOTSUPP => 'Compression method not supported',
            ZipArchive::ER_EOF => 'Premature EOF',
            ZipArchive::ER_INVAL => 'Invalid argument',
            ZipArchive::ER_NOZIP => 'Not a zip archive',
            ZipArchive::ER_INTERNAL => 'Internal error',
            ZipArchive::ER_INCONS => 'Zip archive inconsistent',
            ZipArchive::ER_REMOVE => 'Can not remove file',
            ZipArchive::ER_DELETED => 'Entry has been deleted'
        ];

        return $errors[$error] ?? 'Unknown ZIP error';
    }

    private function formatFileSize($bytes) {
        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        $bytes /= pow(1024, $pow);
        return round($bytes, 2) . ' ' . $units[$pow];
    }
}

// Handle the request
try {
    $handler = new EnhancedUploadHandler();
    $result = $handler->handleUpload();
    echo json_encode($result);
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Server error: ' . $e->getMessage()
    ]);
}
