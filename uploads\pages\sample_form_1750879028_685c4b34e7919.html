<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sample Contact Form - Test Page</title>
    <meta name="description" content="A sample contact form for testing the Webpage Manager application">
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .form-container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        input, textarea, select {
            width: 100%;
            padding: 10px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        input:focus, textarea:focus, select:focus {
            border-color: #4CAF50;
            outline: none;
        }
        .required {
            color: red;
        }
        .checkbox-group, .radio-group {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
        }
        .checkbox-item, .radio-item {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        .checkbox-item input, .radio-item input {
            width: auto;
        }
        .submit-btn {
            background: #4CAF50;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        .submit-btn:hover {
            background: #45a049;
        }
    </style>
</head>
<body>
    <div class="form-container">
        <h1>Contact Us</h1>
        <p>Please fill out this form to get in touch with us.</p>

        <!-- Main Contact Form -->
        <form id="contact-form" action="process_contact.php" method="POST" enctype="multipart/form-data">
            
            <!-- Personal Information -->
            <div class="form-group">
                <label for="first_name">First Name <span class="required">*</span></label>
                <input type="text" id="first_name" name="first_name" required maxlength="50" placeholder="Enter your first name">
            </div>

            <div class="form-group">
                <label for="last_name">Last Name <span class="required">*</span></label>
                <input type="text" id="last_name" name="last_name" required maxlength="50" placeholder="Enter your last name">
            </div>

            <div class="form-group">
                <label for="email">Email Address <span class="required">*</span></label>
                <input type="email" id="email" name="email" required placeholder="<EMAIL>">
            </div>

            <div class="form-group">
                <label for="phone">Phone Number</label>
                <input type="tel" id="phone" name="phone" pattern="[0-9]{3}-[0-9]{3}-[0-9]{4}" placeholder="************">
            </div>

            <div class="form-group">
                <label for="website">Website</label>
                <input type="url" id="website" name="website" placeholder="https://www.example.com">
            </div>

            <!-- Demographics -->
            <div class="form-group">
                <label for="age">Age</label>
                <input type="number" id="age" name="age" min="13" max="120" step="1">
            </div>

            <div class="form-group">
                <label for="birth_date">Date of Birth</label>
                <input type="date" id="birth_date" name="birth_date">
            </div>

            <div class="form-group">
                <label for="preferred_time">Preferred Contact Time</label>
                <input type="time" id="preferred_time" name="preferred_time">
            </div>

            <!-- Preferences -->
            <div class="form-group">
                <label for="country">Country</label>
                <select id="country" name="country" required>
                    <option value="">Select your country</option>
                    <option value="us">United States</option>
                    <option value="ca">Canada</option>
                    <option value="uk">United Kingdom</option>
                    <option value="au">Australia</option>
                    <option value="de">Germany</option>
                    <option value="fr">France</option>
                    <option value="other">Other</option>
                </select>
            </div>

            <div class="form-group">
                <label for="subject">Subject Category</label>
                <select id="subject" name="subject" required>
                    <optgroup label="General Inquiries">
                        <option value="general">General Question</option>
                        <option value="info">Request Information</option>
                    </optgroup>
                    <optgroup label="Support">
                        <option value="technical">Technical Support</option>
                        <option value="billing">Billing Question</option>
                        <option value="bug">Report a Bug</option>
                    </optgroup>
                    <optgroup label="Business">
                        <option value="partnership">Partnership Inquiry</option>
                        <option value="sales">Sales Question</option>
                    </optgroup>
                </select>
            </div>

            <!-- Contact Preferences -->
            <div class="form-group">
                <label>Preferred Contact Method <span class="required">*</span></label>
                <div class="radio-group">
                    <div class="radio-item">
                        <input type="radio" id="contact_email" name="contact_method" value="email" required>
                        <label for="contact_email">Email</label>
                    </div>
                    <div class="radio-item">
                        <input type="radio" id="contact_phone" name="contact_method" value="phone" required>
                        <label for="contact_phone">Phone</label>
                    </div>
                    <div class="radio-item">
                        <input type="radio" id="contact_both" name="contact_method" value="both" required>
                        <label for="contact_both">Both</label>
                    </div>
                </div>
            </div>

            <!-- Interests -->
            <div class="form-group">
                <label>Areas of Interest (select all that apply)</label>
                <div class="checkbox-group">
                    <div class="checkbox-item">
                        <input type="checkbox" id="interest_web" name="interests[]" value="web_development">
                        <label for="interest_web">Web Development</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="interest_mobile" name="interests[]" value="mobile_apps">
                        <label for="interest_mobile">Mobile Apps</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="interest_design" name="interests[]" value="design">
                        <label for="interest_design">Design</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="interest_marketing" name="interests[]" value="marketing">
                        <label for="interest_marketing">Digital Marketing</label>
                    </div>
                </div>
            </div>

            <!-- Message -->
            <div class="form-group">
                <label for="message">Message <span class="required">*</span></label>
                <textarea id="message" name="message" rows="6" cols="50" required minlength="10" maxlength="1000" placeholder="Please describe your inquiry in detail..."></textarea>
            </div>

            <!-- File Upload -->
            <div class="form-group">
                <label for="attachment">Attachment (optional)</label>
                <input type="file" id="attachment" name="attachment" accept=".pdf,.doc,.docx,.txt,.jpg,.png">
            </div>

            <!-- Priority -->
            <div class="form-group">
                <label for="priority">Priority Level</label>
                <input type="range" id="priority" name="priority" min="1" max="5" step="1" value="3">
                <div style="display: flex; justify-content: space-between; font-size: 12px; color: #666;">
                    <span>Low</span>
                    <span>Medium</span>
                    <span>High</span>
                </div>
            </div>

            <!-- Newsletter -->
            <div class="form-group">
                <div class="checkbox-item">
                    <input type="checkbox" id="newsletter" name="newsletter" value="yes">
                    <label for="newsletter">Subscribe to our newsletter</label>
                </div>
            </div>

            <!-- Hidden Fields -->
            <input type="hidden" name="form_source" value="website_contact">
            <input type="hidden" name="timestamp" value="">

            <!-- Submit -->
            <div class="form-group">
                <button type="submit" class="submit-btn">Send Message</button>
                <button type="reset" style="margin-left: 10px; padding: 12px 20px; background: #ccc; border: none; border-radius: 5px; cursor: pointer;">Reset Form</button>
            </div>
        </form>

        <!-- Secondary Newsletter Form -->
        <div style="margin-top: 40px; padding-top: 20px; border-top: 1px solid #ddd;">
            <h3>Quick Newsletter Signup</h3>
            <form id="newsletter-form" action="newsletter.php" method="POST">
                <div style="display: flex; gap: 10px; align-items: end;">
                    <div style="flex: 1;">
                        <label for="newsletter_email">Email Address</label>
                        <input type="email" id="newsletter_email" name="email" required placeholder="Enter your email">
                    </div>
                    <div>
                        <button type="submit" style="padding: 10px 20px; background: #2196F3; color: white; border: none; border-radius: 5px; cursor: pointer;">Subscribe</button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <script>
        // Set timestamp when page loads
        document.querySelector('input[name="timestamp"]').value = new Date().toISOString();
        
        // Update priority display
        document.getElementById('priority').addEventListener('input', function() {
            const value = this.value;
            const labels = ['', 'Low', 'Low-Medium', 'Medium', 'Medium-High', 'High'];
            console.log('Priority: ' + labels[value]);
        });
    </script>
</body>
</html>
