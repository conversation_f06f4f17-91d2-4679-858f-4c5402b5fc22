<?php
/**
 * Form Submission Handler for Shared Pages
 * Collects and stores form data from shared pages
 */

require_once 'config/database.php';

header('Content-Type: application/json');

try {
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Only POST requests allowed');
    }

    $shareToken = $_POST['_share_token'] ?? null;
    $formId = $_POST['_form_id'] ?? null;
    $pageId = $_POST['_page_id'] ?? null;

    if (!$shareToken && !$pageId) {
        throw new Exception('Missing page or share identifier');
    }

    $database = new Database();
    $db = $database->getConnection();

    // Validate share token if provided
    $shareInfo = null;
    if ($shareToken) {
        $sql = "SELECT ps.*, p.id as page_id, p.title as page_title 
                FROM page_shares ps 
                JOIN pages p ON ps.page_id = p.id 
                WHERE ps.share_token = ? AND ps.is_active = 1";
        $stmt = $db->prepare($sql);
        $stmt->execute([$shareToken]);
        $shareInfo = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$shareInfo) {
            throw new Exception('Invalid or expired share token');
        }

        // Check if forms are allowed
        if (!$shareInfo['show_forms']) {
            throw new Exception('Form submissions are disabled for this shared page');
        }

        $pageId = $shareInfo['page_id'];
    }

    // Get page information
    $sql = "SELECT * FROM pages WHERE id = ?";
    $stmt = $db->prepare($sql);
    $stmt->execute([$pageId]);
    $page = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$page) {
        throw new Exception('Page not found');
    }

    // Get form information if form ID provided
    $formInfo = null;
    if ($formId) {
        $sql = "SELECT * FROM forms WHERE id = ? AND page_id = ?";
        $stmt = $db->prepare($sql);
        $stmt->execute([$formId, $pageId]);
        $formInfo = $stmt->fetch(PDO::FETCH_ASSOC);
    }

    // Prepare form data (exclude system fields)
    $formData = $_POST;
    $systemFields = ['_share_token', '_form_id', '_page_id', '_form_name'];
    foreach ($systemFields as $field) {
        unset($formData[$field]);
    }

    // Get visitor information
    $ipAddress = $_SERVER['REMOTE_ADDR'] ?? '';
    $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
    $referrer = $_SERVER['HTTP_REFERER'] ?? '';

    // Store form submission
    $sql = "INSERT INTO form_submissions (
                page_id, form_id, share_id, submission_data, ip_address,
                user_agent, referrer, submitted_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, NOW())";

    $stmt = $db->prepare($sql);
    $stmt->execute([
        $pageId,
        $formId,
        $shareInfo['id'] ?? null,
        json_encode($formData),
        $ipAddress,
        $userAgent,
        $referrer
    ]);

    $submissionId = $db->lastInsertId();

    // Log the submission
    if ($shareInfo) {
        logShareAccess($db, $shareInfo['id'], 'form_submission');
    }

    // Log activity
    $sql = "INSERT INTO activity_log (user_id, action, entity_type, entity_id, new_values, ip_address, user_agent) 
            VALUES (?, ?, ?, ?, ?, ?, ?)";
    $stmt = $db->prepare($sql);
    $stmt->execute([
        null, // No user for public submissions
        'form_submitted',
        'form_submission',
        $submissionId,
        json_encode([
            'page_id' => $pageId,
            'form_id' => $formId,
            'share_id' => $shareInfo['id'] ?? null,
            'field_count' => count($formData),
            'is_shared' => !empty($shareInfo)
        ]),
        $ipAddress,
        $userAgent
    ]);

    // Return success response
    echo json_encode([
        'success' => true,
        'message' => 'Form submitted successfully!',
        'submission_id' => $submissionId,
        'redirect_url' => $_POST['_redirect_url'] ?? null
    ]);

} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

function logShareAccess($db, $shareId, $accessType = 'view') {
    $ip = $_SERVER['REMOTE_ADDR'] ?? '';
    $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
    $referrer = $_SERVER['HTTP_REFERER'] ?? '';
    
    $sql = "INSERT INTO share_access_log (share_id, ip_address, user_agent, referrer, access_type) 
            VALUES (?, ?, ?, ?, ?)";
    $stmt = $db->prepare($sql);
    $stmt->execute([$shareId, $ip, $userAgent, $referrer, $accessType]);
}
?>
