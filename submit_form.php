<?php
/**
 * Form Submission Handler for Shared Pages
 * Collects and stores form data from shared pages
 */

require_once 'config/database.php';

header('Content-Type: application/json');

try {
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Only POST requests allowed');
    }

    $shareToken = $_POST['_share_token'] ?? null;
    $formId = $_POST['_form_id'] ?? null;
    $pageId = $_POST['_page_id'] ?? null;

    if (!$shareToken && !$pageId) {
        throw new Exception('Missing page or share identifier');
    }

    $database = new Database();
    $db = $database->getConnection();

    // Validate share token if provided
    $shareInfo = null;
    if ($shareToken) {
        $sql = "SELECT ps.*, p.id as page_id, p.title as page_title 
                FROM page_shares ps 
                JOIN pages p ON ps.page_id = p.id 
                WHERE ps.share_token = ? AND ps.is_active = 1";
        $stmt = $db->prepare($sql);
        $stmt->execute([$shareToken]);
        $shareInfo = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$shareInfo) {
            throw new Exception('Invalid or expired share token');
        }

        // Check if forms are allowed
        if (!$shareInfo['show_forms']) {
            throw new Exception('Form submissions are disabled for this shared page');
        }

        $pageId = $shareInfo['page_id'];
    }

    // Get page information
    $sql = "SELECT * FROM pages WHERE id = ?";
    $stmt = $db->prepare($sql);
    $stmt->execute([$pageId]);
    $page = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$page) {
        throw new Exception('Page not found');
    }

    // Get form information if form ID provided
    $formInfo = null;
    if ($formId) {
        $sql = "SELECT * FROM forms WHERE id = ? AND page_id = ?";
        $stmt = $db->prepare($sql);
        $stmt->execute([$formId, $pageId]);
        $formInfo = $stmt->fetch(PDO::FETCH_ASSOC);
    }

    // Prepare form data (exclude system fields)
    $formData = $_POST;
    $systemFields = ['_share_token', '_form_id', '_page_id', '_form_name'];
    foreach ($systemFields as $field) {
        unset($formData[$field]);
    }

    // Get visitor information
    $ipAddress = $_SERVER['REMOTE_ADDR'] ?? '';
    $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
    $referrer = $_SERVER['HTTP_REFERER'] ?? '';

    // Store form submission with automatic column detection
    try {
        // Check what columns exist in the table
        $sql = "DESCRIBE form_submissions";
        $stmt = $db->prepare($sql);
        $stmt->execute();
        $columns = array_column($stmt->fetchAll(PDO::FETCH_ASSOC), 'Field');

        // Determine which data column to use
        $dataColumn = 'submission_data';
        if (in_array('form_data', $columns) && !in_array('submission_data', $columns)) {
            $dataColumn = 'form_data';
        }

        // Build the INSERT query based on available columns
        $insertColumns = ['page_id', $dataColumn];
        $insertValues = [$pageId, json_encode($formData)];
        $placeholders = ['?', '?'];

        // Add optional columns if they exist
        $optionalColumns = [
            'form_id' => $formId,
            'share_id' => $shareInfo['id'] ?? null,
            'ip_address' => $ipAddress,
            'user_agent' => $userAgent,
            'referrer' => $referrer
        ];

        foreach ($optionalColumns as $column => $value) {
            if (in_array($column, $columns)) {
                $insertColumns[] = $column;
                $insertValues[] = $value;
                $placeholders[] = '?';
            }
        }

        // Add timestamp column
        if (in_array('submitted_at', $columns)) {
            $insertColumns[] = 'submitted_at';
            $placeholders[] = 'NOW()';
        } elseif (in_array('created_at', $columns)) {
            $insertColumns[] = 'created_at';
            $placeholders[] = 'NOW()';
        }

        $sql = "INSERT INTO form_submissions (" . implode(', ', $insertColumns) . ")
                VALUES (" . implode(', ', $placeholders) . ")";

        $stmt = $db->prepare($sql);
        $stmt->execute($insertValues);

    } catch (PDOException $e) {
        // Fallback for minimal submission
        error_log("Form submission failed, using minimal fallback: " . $e->getMessage());

        // Determine data column for fallback
        $dataColumn = 'submission_data';
        if (strpos($e->getMessage(), 'submission_data') !== false) {
            $dataColumn = 'form_data';
        }

        $sql = "INSERT INTO form_submissions (page_id, $dataColumn) VALUES (?, ?)";
        $stmt = $db->prepare($sql);
        $stmt->execute([$pageId, json_encode($formData)]);
    }

    $submissionId = $db->lastInsertId();

    // Log the submission
    if ($shareInfo) {
        logShareAccess($db, $shareInfo['id'], 'form_submission');
    }

    // Log activity
    $sql = "INSERT INTO activity_log (user_id, action, entity_type, entity_id, new_values, ip_address, user_agent) 
            VALUES (?, ?, ?, ?, ?, ?, ?)";
    $stmt = $db->prepare($sql);
    $stmt->execute([
        null, // No user for public submissions
        'form_submitted',
        'form_submission',
        $submissionId,
        json_encode([
            'page_id' => $pageId,
            'form_id' => $formId,
            'share_id' => $shareInfo['id'] ?? null,
            'field_count' => count($formData),
            'is_shared' => !empty($shareInfo)
        ]),
        $ipAddress,
        $userAgent
    ]);

    // Return success response
    echo json_encode([
        'success' => true,
        'message' => 'Form submitted successfully!',
        'submission_id' => $submissionId,
        'redirect_url' => $_POST['_redirect_url'] ?? null
    ]);

} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

function logShareAccess($db, $shareId, $accessType = 'view') {
    try {
        // First verify the share exists to prevent foreign key constraint violation
        $sql = "SELECT id FROM page_shares WHERE id = ? AND is_active = 1";
        $stmt = $db->prepare($sql);
        $stmt->execute([$shareId]);

        if (!$stmt->fetch()) {
            // Share doesn't exist or is inactive, don't log
            error_log("Attempted to log access for non-existent share ID: $shareId");
            return false;
        }

        $ip = $_SERVER['REMOTE_ADDR'] ?? '';
        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
        $referrer = $_SERVER['HTTP_REFERER'] ?? '';

        $sql = "INSERT INTO share_access_log (share_id, ip_address, user_agent, referrer, access_type)
                VALUES (?, ?, ?, ?, ?)";
        $stmt = $db->prepare($sql);
        $stmt->execute([$shareId, $ip, $userAgent, $referrer, $accessType]);

        return true;
    } catch (PDOException $e) {
        // Log the error but don't break the operation
        error_log("Failed to log share access: " . $e->getMessage());
        return false;
    }
}
?>
